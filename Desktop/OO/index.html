`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Chat AI - Single Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            perspective: 1000px; /* For 3D effects */
        }

        :root {
            --primary-color: #4a90e2;
            --secondary-color: #7b68ee;
            --accent-color: #50c878;
            --background-dark: #0f1419;
            --background-medium: #1a1f2e;
            --background-light: #2a3441;
            --text-primary: #e8eaed;
            --text-secondary: #9aa0a6;
            --border-color: rgba(255, 255, 255, 0.1);
            --shadow-color: rgba(0, 0, 0, 0.3);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-medium) 100%);
            background-attachment: fixed;
            overflow-x: hidden;
            position: relative;
            color: var(--text-primary);
            /* Optimize for high refresh rates */
            will-change: transform;
            backface-visibility: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .parallax-bg {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            z-index: 0;
            pointer-events: none;
            background:
                radial-gradient(circle at 60% 40%, rgba(74, 144, 226, 0.15) 0%, transparent 70%),
                radial-gradient(circle at 30% 70%, rgba(123, 104, 238, 0.1) 0%, transparent 80%),
                radial-gradient(circle at 80% 20%, rgba(80, 200, 120, 0.08) 0%, transparent 60%);
            will-change: background-position, transform;
            transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            /* High refresh rate optimization */
            transform: translateZ(0);
        }

        body.dark-mode {
            background: var(--background-dark);
            color: var(--text-primary);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            transform-style: preserve-3d;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            /* Responsive container */
            width: 100%;
        }

        h1 {
            text-align: center;
            font-size: clamp(2rem, 4vw, 3rem);
            margin-bottom: 30px;
            color: var(--primary-color);
            text-shadow: 0 2px 20px rgba(74, 144, 226, 0.3);
            transform-style: preserve-3d;
            font-weight: 700;
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .step-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 1px 3px var(--shadow-color);
        }

        .table-name {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
            text-shadow: 0 1px 3px var(--shadow-color);
        }

        .main-content {
            display: grid;
            grid-template-columns: minmax(320px, 380px) 1fr;
            gap: 24px;
            min-height: 600px;
            transform-style: preserve-3d;
            /* Responsive grid */
            transition: grid-template-columns 0.3s ease;
        }

        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px) saturate(180%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid var(--glass-border);
            height: fit-content;
            transform-style: preserve-3d;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 2px 8px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            opacity: 0.5;
        }

        .content-area {
            background: var(--glass-bg);
            backdrop-filter: blur(20px) saturate(180%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid var(--glass-border);
            min-height: 600px;
            transform-style: preserve-3d;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 2px 8px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .content-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
            opacity: 0.5;
        }

        .step-section {
            margin-bottom: 20px;
            padding: 18px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            background: rgba(255, 255, 255, 0.02);
            position: relative;
            transform: translateZ(0);
        }

        .step-section:hover:not(.disabled) {
            transform: translateY(-2px) translateZ(0);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .step-section.active {
            background: rgba(74, 144, 226, 0.1);
            border-color: var(--primary-color);
            box-shadow: 0 0 20px rgba(74, 144, 226, 0.2);
        }

        .step-section.completed {
            background: rgba(80, 200, 120, 0.1);
            border-color: var(--accent-color);
            box-shadow: 0 0 20px rgba(80, 200, 120, 0.15);
        }

        .step-section.disabled {
            opacity: 0.4;
            pointer-events: none;
            filter: grayscale(0.5);
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 2px solid transparent;
        }

        .step-section.active .step-number {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 0 15px rgba(74, 144, 226, 0.4);
            transform: scale(1.1);
        }

        .step-section.completed .step-number {
            background: var(--accent-color);
            color: white;
            box-shadow: 0 0 15px rgba(80, 200, 120, 0.4);
        }

        .form-group {
            margin-bottom: 18px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-1px);
        }

        .form-control::placeholder {
            color: var(--text-secondary);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            margin-right: 12px;
            margin-bottom: 12px;
            position: relative;
            overflow: hidden;
            transform: translateZ(0);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0) scale(0.98);
            box-shadow: 0 2px 10px rgba(74, 144, 226, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none !important;
        }

        .status-bar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px) saturate(180%);
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid var(--glass-border);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .status-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            transition: background-color 0.3s ease;
        }

        .status-dot.connected {
            background: #00ff00;
        }

        .status-dot.connecting {
            background: #ffaa00;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* High refresh rate optimizations */
        @media (min-resolution: 120dpi) {
            * {
                will-change: auto;
            }

            .parallax-bg {
                transition: all 0.05s linear;
            }

            .btn, .form-control, .table-item, .step-section {
                transition-duration: 0.15s;
            }
        }

        @media (min-resolution: 144dpi) {
            .parallax-bg {
                transition: all 0.033s linear;
            }

            .btn, .form-control, .table-item, .step-section {
                transition-duration: 0.1s;
            }
        }

        .content-panel {
            display: none;
        }

        .content-panel.active {
            display: block;
        }

        .schema-browser {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .table-item {
            padding: 16px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            position: relative;
            transform: translateZ(0);
        }

        .table-item:hover {
            background: rgba(74, 144, 226, 0.08);
            transform: translateY(-4px) scale(1.02) rotateX(2deg);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
            z-index: 2;
        }

        .table-info {
            font-size: 0.8rem;
            color: #888;
        }

        .query-area {
            margin-bottom: 20px;
        }

        .query-input {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #e0e0e0;
            resize: vertical;
            font-family: inherit;
        }

        .results-area {
            max-height: 400px;
            overflow: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 15px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .results-table th,
        .results-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .results-table th {
            background: rgba(74, 144, 226, 0.1);
            color: var(--primary-color);
            position: sticky;
            top: 0;
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--primary-color);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 12px 16px;
            border-radius: 8px;
            margin: 12px 0;
            border-left: 4px solid #ff6b6b;
            backdrop-filter: blur(10px);
        }

        .success {
            color: var(--accent-color);
            background: rgba(80, 200, 120, 0.1);
            padding: 12px 16px;
            border-radius: 8px;
            margin: 12px 0;
            border-left: 4px solid var(--accent-color);
            backdrop-filter: blur(10px);
        }

        /* Enhanced responsive design */
        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
                padding: 16px;
            }

            .main-content {
                grid-template-columns: minmax(300px, 350px) 1fr;
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .sidebar {
                order: 2;
                margin-top: 20px;
            }

            .container {
                padding: 12px;
            }

            h1 {
                margin-bottom: 20px;
            }
        }

        @media (max-width: 480px) {
            .main-content, .container {
                padding: 8px !important;
            }

            .sidebar, .content-area {
                min-height: 200px;
                padding: 16px;
                border-radius: 12px;
            }

            h1 {
                font-size: 1.8rem;
                margin-bottom: 16px;
            }

            .step-section {
                padding: 14px;
                margin-bottom: 16px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 320px) {
            .container {
                padding: 4px !important;
            }

            .sidebar, .content-area {
                padding: 12px;
                border-radius: 8px;
            }

            .main-content {
                gap: 12px;
            }
        }

        .schema-sidebar {
            position: fixed;
            right: 0;
            top: 60px;
            width: 250px;
            height: calc(100vh - 60px);
            background-color: #f8f9fa;
            border-left: 1px solid #dee2e6;
            padding: 15px;
            overflow-y: auto;
            z-index: 100;
        }

        .schema-header {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .schema-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        #selected-database-name {
            display: block;
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }

        .schema-content {
            font-size: 14px;
        }

        .schema-table {
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }

        .schema-table-header {
            background-color: #e9ecef;
            padding: 8px 12px;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
        }

        .schema-table-columns {
            padding: 8px 12px;
            background-color: white;
        }

        .schema-column {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .schema-column:last-child {
            border-bottom: none;
        }

        .schema-column-name {
            font-weight: 500;
        }

        .schema-column-type {
            color: #6c757d;
            font-size: 12px;
        }

        .schema-placeholder {
            color: #6c757d;
            text-align: center;
            padding: 20px 0;
        }

        /* Adjust content area to make room for sidebar */
        .content-area {
            margin-right: 250px;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .schema-sidebar {
                width: 200px;
            }
            .content-area {
                margin-right: 200px;
            }
        }

        @media (max-width: 768px) {
            .schema-sidebar {
                position: static;
                width: 100%;
                height: auto;
                border-left: none;
                border-bottom: 1px solid #dee2e6;
                margin-bottom: 15px;
            }
            .content-area {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="parallax-bg"></div>
    <div class="container">
        <h1>SQL Chat AI</h1>
        
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-indicator">
                <span class="status-dot" id="status-dot"></span>
                <span id="status-text">Ready to connect</span>
            </div>
            <div class="status-controls">
                <button id="theme-toggle" class="btn btn-secondary" style="padding: 8px 12px; margin: 0;">
                    🌙
                </button>
                <div id="connection-info"></div>
            </div>
        </div>

        <div class="main-content">
            <!-- Sidebar with Steps -->
            <div class="sidebar">
                <!-- Step 1: Database Type -->
                <div class="step-section active" id="step-1">
                    <div class="step-header">
                        <span class="step-number">1</span>
                        <span>Database Type</span>
                    </div>
                    <div class="form-group">
                        <label for="db-type">Select Database Type</label>
                        <select id="db-type" class="form-control">
                            <option value="">Choose database...</option>
                            <option value="mysql">MySQL</option>
                            <option value="postgresql">PostgreSQL</option>
                            <option value="mongodb">MongoDB</option>
                            <option value="sqlite">SQLite</option>
                        </select>
                    </div>
                </div>

                <!-- Step 2: Connection Details -->
                <div class="step-section disabled" id="step-2">
                    <div class="step-header">
                        <span class="step-number">2</span>
                        <span>Connection</span>
                    </div>
                    <div id="connection-fields"></div>
                    <button id="connect-btn" class="btn btn-primary" disabled>Connect</button>
                </div>

                <!-- Step 3: Database Selection -->
                <div class="step-section disabled" id="step-3">
                    <div class="step-header">
                        <span class="step-number">3</span>
                        <span>Select Database</span>
                    </div>
                    <div class="form-group">
                        <label for="database-select">Available Databases</label>
                        <select id="database-select" class="form-control">
                            <option value="">Select database...</option>
                        </select>
                    </div>
                </div>

                <!-- Step 4: Query Interface -->
                <div class="step-section disabled" id="step-4">
                    <div class="step-header">
                        <span class="step-number">4</span>
                        <span>Query</span>
                    </div>
                    <div class="query-area">
                        <textarea 
                            id="query-input" 
                            class="query-input" Can we add 3d effects and parallax effects
Also give dark and, style look also screen size adjustable with 30 to 144 hz refresh rate screen
                            placeholder="Ask a question in natural language...
Example: 'Show me all users who registered last month'"
                        ></textarea>
                        <button id="execute-btn" class="btn btn-primary">Execute Query</button>
                        <button id="clear-btn" class="btn btn-secondary">Clear</button>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="content-area">
                <!-- Welcome Panel -->
                <div class="content-panel active" id="welcome-panel">
                    <h2>Welcome to SQL Chat AI</h2>
                    <p>Follow these simple steps to get started:</p>
                    <ol style="margin: 20px 0; padding-left: 20px;">
                        <li>Select your database type</li>
                        <li>Enter connection details</li>
                        <li>Choose a database to work with</li>
                        <li>Ask questions in natural language</li>
                    </ol>
                    <p>Start by selecting a database type from the sidebar.</p>
                </div>

                <!-- Connection Panel -->
                <div class="content-panel" id="connection-panel">
                    <h2>Database Connection</h2>
                    <div id="connection-status"></div>
                </div>

                <!-- Schema Panel -->
                <div class="content-panel" id="schema-panel">
                    <h2>Database Schema</h2>
                    <div id="selected-database-name"></div>
                    <div class="schema-browser" id="schema-display">
                        <div class="loading">Loading schema...</div>
                    </div>
                    <h3>Sample Data</h3>
                    <div class="results-area" id="sample-data">
                        <div class="loading">Select a table to view sample data</div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="content-panel" id="results-panel">
                    <h2>Query Results</h2>
                    <div id="query-info"></div>
                    <div class="results-area" id="query-results">
                        <div class="loading">Execute a query to see results</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SQLChatApp {
            constructor() {
                this.currentConnection = null;
                this.apiBase = 'http://localhost:3002/api';
                this.currentStep = 1;
                this.schema = null;
                this.selectedDatabase = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupThemeToggle();
                this.showStatus('Ready to connect', 'ready');
                console.log('SQL Chat App initialized');
            }

            setupEventListeners() {
                // Database type selection
                document.getElementById('db-type').addEventListener('change', (e) => {
                    this.handleDatabaseTypeChange(e.target.value);
                });

                // Connect button
                document.getElementById('connect-btn').addEventListener('click', () => {
                    this.handleConnect();
                });

                // Database selection
                document.getElementById('database-select').addEventListener('change', (e) => {
                    this.handleDatabaseSelect(e.target.value);
                });

                // Query execution
                document.getElementById('execute-btn').addEventListener('click', () => {
                    this.handleQueryExecute();
                });

                // Clear button
                document.getElementById('clear-btn').addEventListener('click', () => {
                    this.handleClear();
                });
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('theme-toggle');
                const savedTheme = localStorage.getItem('theme') || 'dark';

                this.setTheme(savedTheme);

                themeToggle.addEventListener('click', () => {
                    const currentTheme = document.body.classList.contains('light-mode') ? 'light' : 'dark';
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    this.setTheme(newTheme);
                    localStorage.setItem('theme', newTheme);
                });
            }

            setTheme(theme) {
                const themeToggle = document.getElementById('theme-toggle');

                if (theme === 'light') {
                    document.body.classList.add('light-mode');
                    document.body.classList.remove('dark-mode');
                    themeToggle.textContent = '☀️';

                    // Update CSS variables for light mode
                    document.documentElement.style.setProperty('--background-dark', '#f8f9fa');
                    document.documentElement.style.setProperty('--background-medium', '#ffffff');
                    document.documentElement.style.setProperty('--background-light', '#e9ecef');
                    document.documentElement.style.setProperty('--text-primary', '#212529');
                    document.documentElement.style.setProperty('--text-secondary', '#6c757d');
                    document.documentElement.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.8)');
                    document.documentElement.style.setProperty('--glass-border', 'rgba(0, 0, 0, 0.1)');
                } else {
                    document.body.classList.add('dark-mode');
                    document.body.classList.remove('light-mode');
                    themeToggle.textContent = '🌙';

                    // Reset to dark mode variables
                    document.documentElement.style.setProperty('--background-dark', '#0f1419');
                    document.documentElement.style.setProperty('--background-medium', '#1a1f2e');
                    document.documentElement.style.setProperty('--background-light', '#2a3441');
                    document.documentElement.style.setProperty('--text-primary', '#e8eaed');
                    document.documentElement.style.setProperty('--text-secondary', '#9aa0a6');
                    document.documentElement.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.05)');
                    document.documentElement.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.1)');
                }
            }

            // Step 1: Database Type Selection
            handleDatabaseTypeChange(dbType) {
                if (!dbType) return;

                console.log('Database type selected:', dbType);
                this.loadConnectionFields(dbType);
                this.activateStep(2);
                this.showPanel('connection-panel');
            }

            loadConnectionFields(dbType) {
                const fieldsContainer = document.getElementById('connection-fields');
                
                const fieldConfigs = {
                    mysql: [
                        { name: 'host', label: 'Host', type: 'text', default: 'localhost' },
                        { name: 'port', label: 'Port', type: 'number', default: '3306' },
                        { name: 'username', label: 'Username', type: 'text', default: 'root' },
                        { name: 'password', label: 'Password', type: 'password', default: '' }
                    ],
                    postgresql: [
                        { name: 'host', label: 'Host', type: 'text', default: 'localhost' },
                        { name: 'port', label: 'Port', type: 'number', default: '5432' },
                        { name: 'username', label: 'Username', type: 'text', default: 'postgres' },
                        { name: 'password', label: 'Password', type: 'password', default: '' }
                    ],
                    mongodb: [
                        { name: 'host', label: 'Host', type: 'text', default: 'localhost' },
                        { name: 'port', label: 'Port', type: 'number', default: '27017' },
                        { name: 'username', label: 'Username', type: 'text', default: '' },
                        { name: 'password', label: 'Password', type: 'password', default: '' }
                    ],
                    sqlite: [
                        { name: 'database_path', label: 'Database Path', type: 'text', default: './databases/default.db' }
                    ]
                };

                const fields = fieldConfigs[dbType] || [];
                fieldsContainer.innerHTML = fields.map(field => `
                    <div class="form-group">
                        <label for="field-${field.name}">${field.label}</label>
                        <input 
                            type="${field.type}" 
                            id="field-${field.name}" 
                            class="form-control" 
                            value="${field.default}"
                            placeholder="Enter ${field.label.toLowerCase()}"
                        >
                    </div>
                `).join('');

                document.getElementById('connect-btn').disabled = false;
            }

        // Step 2: Database Connection
        async handleConnect() {
            const dbType = document.getElementById('db-type').value;
            const connectBtn = document.getElementById('connect-btn');
            
            if (!this.validateConnectionForm(dbType)) return;

            connectBtn.disabled = true;
            connectBtn.textContent = 'Connecting...';
            this.showStatus('Connecting to database...', 'connecting');

            try {
                const config = this.getConnectionConfig(dbType);
                
                console.log('Sending request to:', `${this.apiBase}/database/connect`);
                console.log('Request payload:', { type: dbType, database: '', config });
                
                const response = await fetch(`${this.apiBase}/database/connect`, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        type: dbType,
                        database: 'test',
                        config: config
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('Non-JSON response:', textResponse);
                    throw new Error(`Server returned ${contentType}: ${textResponse.substring(0, 200)}`);
                }

                const data = await response.json();
                console.log('Parsed response:', data);
                
                if (data.success) {
                    this.currentConnection = {
                        type: dbType,
                        sessionId: data.session_id
                    };
                    
                    this.showStatus('Connected! Loading databases...', 'connected');
                    this.completeStep(2);
                    await this.loadDatabases();
                    
                } else {
                    throw new Error(data.message || 'Connection failed');
                }
            } catch (error) {
                console.error('Connection error details:', error);
                
                if (error instanceof SyntaxError && error.message.includes('JSON.parse')) {
                    this.showStatus('Server returned invalid response. Check server logs.', 'error');
                } else if (error.name === 'TypeError' && error.message.includes('NetworkError')) {
                    this.showStatus('Network error: Please ensure Node.js server is running on port 3002', 'error');
                } else {
                    this.showStatus(`Connection error: ${error.message}`, 'error');
                }
            } finally {
                connectBtn.disabled = false;
                connectBtn.textContent = 'Connect';
            }
        }

            validateConnectionForm(dbType) {
                const requiredFields = {
                    mysql: ['host', 'port', 'username', 'password'],
                    postgresql: ['host', 'port', 'username', 'password'],
                    mongodb: ['host', 'port'],
                    sqlite: ['database_path']
                };

                const fields = requiredFields[dbType] || [];
                for (const field of fields) {
                    const input = document.getElementById(`field-${field}`);
                    if (!input || !input.value.trim()) {
                        this.showStatus(`Please fill in ${field.replace('_', ' ')}`, 'error');
                        return false;
                    }
                }
                return true;
            }

            getConnectionConfig(dbType) {
                const config = {};
                const fieldMap = {
                    mysql: ['host', 'port', 'username', 'password'],
                    postgresql: ['host', 'port', 'username', 'password'],
                    mongodb: ['host', 'port', 'username', 'password'],
                    sqlite: ['database_path']
                };

                const fields = fieldMap[dbType] || [];
                fields.forEach(field => {
                    const input = document.getElementById(`field-${field}`);
                    if (input && input.value) {
                        config[field] = field === 'port' ? parseInt(input.value) : input.value;
                    }
                });

                return config;
            }

            async loadDatabases() {
                try {
                    console.log('🔍 Loading databases for session:', this.currentConnection.sessionId);
                    
                    const url = `${this.apiBase}/databases?session_id=${this.currentConnection.sessionId}`;
                    console.log('📡 Fetching from:', url);
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    console.log('📥 Response status:', response.status);
                    console.log('📥 Response headers:', [...response.headers.entries()]);
                    
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }
                    
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        const textResponse = await response.text();
                        console.error('❌ Non-JSON response:', textResponse);
                        throw new Error(`Expected JSON, got ${contentType}: ${textResponse.substring(0, 200)}`);
                    }
                    
                    const data = await response.json();
                    console.log('📊 Databases response:', data);
                    
                    if (data.success) {
                        const select = document.getElementById('database-select');
                        select.innerHTML = '<option value="">Select database...</option>';
                        
                        data.databases.forEach(db => {
                            const option = document.createElement('option');
                            option.value = db.name;
                            option.textContent = `${db.name} (${db.type})`;
                            select.appendChild(option);
                        });

                        this.activateStep(3);
                        this.showStatus(`Found ${data.databases.length} databases`, 'connected');
                        
                    } else {
                        throw new Error(data.message || 'Failed to load databases');
                    }
                } catch (error) {
                    console.error('❌ Error loading databases:', error);
                    
                    if (error instanceof TypeError && error.message.includes('NetworkError')) {
                        this.showStatus('Network error: Check if Node.js server is running on port 3002', 'error');
                    } else {
                        this.showStatus(`Error loading databases: ${error.message}`, 'error');
                    }
                }
            }

            // Step 3: Database Selection
            async handleDatabaseSelect(database) {
                if (!database) return;

                this.selectedDatabase = database;
                this.showStatus(`Loading schema for ${database}...`, 'connecting');

                try {
                    // Select database
                    const selectResponse = await fetch(`${this.apiBase}/select-database`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            session_id: this.currentConnection.sessionId,
                            database: database
                        })
                    });

                    const selectData = await selectResponse.json();
                    if (!selectData.success) {
                        throw new Error(selectData.message || 'Failed to select database');
                    }

                    // Get tables for the selected database
                    const tablesResponse = await fetch(`${this.apiBase}/tables?session_id=${this.currentConnection.sessionId}&database=${database}`);
                    const tablesData = await tablesResponse.json();
                    if (!tablesData.success) {
                        throw new Error(tablesData.message || 'Failed to load tables');
                    }

                    // Get schema
                    const schemaResponse = await fetch(`${this.apiBase}/schema?session_id=${this.currentConnection.sessionId}&database=${this.selectedDatabase}`);
                    const schemaData = await schemaResponse.json();
                    
                    if (schemaData.success) {
                        this.schema = schemaData.schema;
                        this.displaySchema(tablesData.tables, schemaData.schema);
                        this.completeStep(3);
                        this.activateStep(4);
                        this.showPanel('schema-panel');
                        this.showStatus(`Connected to ${database}`, 'connected');
                        
                    } else {
                        throw new Error(schemaData.message || 'Failed to load schema');
                    }
                } catch (error) {
                    console.error('Error selecting database:', error);
                    this.showStatus(`Error: ${error.message}`, 'error');
                }
            }

            displaySchema(tables, schema) {
                const schemaDisplay = document.getElementById('schema-display');
                const databaseName = document.getElementById('selected-database-name');
                
                // Set the database name
                databaseName.textContent = this.selectedDatabase;
                
                // Clear previous schema
                schemaDisplay.innerHTML = '';
                
                if (!tables || tables.length === 0) {
                    schemaDisplay.innerHTML = '<div class="schema-placeholder">No tables found</div>';
                    return;
                }
                
                // Create HTML for each table
                tables.forEach(table => {
                    const tableDiv = document.createElement('div');
                    tableDiv.className = 'schema-table';
                    
                    // Table header
                    const tableHeader = document.createElement('div');
                    tableHeader.className = 'schema-table-header';
                    tableHeader.textContent = table.name;
                    tableHeader.onclick = () => this.handleTableSelect(table.name);
                    tableDiv.appendChild(tableHeader);
                    
                    // Table columns
                    const columnsDiv = document.createElement('div');
                    columnsDiv.className = 'schema-table-columns';
                    
                    // Get columns for this table
                    const columns = schema[table.name]?.columns || [];
                    
                    if (columns.length === 0) {
                        columnsDiv.innerHTML = '<div class="schema-placeholder">No columns found</div>';
                    } else {
                        columns.forEach(column => {
                            const columnDiv = document.createElement('div');
                            columnDiv.className = 'schema-column';
                            
                            const nameSpan = document.createElement('span');
                            nameSpan.className = 'schema-column-name';
                            nameSpan.textContent = column;
                            
                            const typeSpan = document.createElement('span');
                            typeSpan.className = 'schema-column-type';
                            // Try to get column type if available
                            const columnInfo = schema[table.name]?.columnInfo?.[column];
                            typeSpan.textContent = columnInfo?.type || '';
                            
                            columnDiv.appendChild(nameSpan);
                            columnDiv.appendChild(typeSpan);
                            columnsDiv.appendChild(columnDiv);
                        });
                    }
                    
                    tableDiv.appendChild(columnsDiv);
                    schemaDisplay.appendChild(tableDiv);
                });
            }

            handleTableSelect(tableName) {
                this.selectedTable = tableName;
                
                // Highlight the selected table
                const tableDivs = document.querySelectorAll('.schema-table-header');
                tableDivs.forEach(div => {
                    if (div.textContent === tableName) {
                        div.classList.add('selected');
                    } else {
                        div.classList.remove('selected');
                    }
                });
                
                // Update the query input placeholder
                const queryInput = document.getElementById('query-input');
                queryInput.placeholder = `Ask a question about ${tableName} (e.g., "Show all records in ${tableName}")`;
                
                // Show a message
                this.showStatus(`Selected table: ${tableName}`, 'info');
            }

            async loadSampleData(tableName) {
                const sampleArea = document.getElementById('sample-data');
                sampleArea.innerHTML = '<div class="loading">Loading sample data...</div>';

                try {
                    const response = await fetch(`${this.apiBase}/table-data?session_id=${this.currentConnection.sessionId}&table=${tableName}&limit=5`);
                    const data = await response.json();
                    
                    if (data.success && data.data.length > 0) {
                        this.displayResults(data.data, sampleArea, `Sample from ${tableName}`);
                    } else {
                        sampleArea.innerHTML = '<div class="loading">No data available</div>';
                    }
                } catch (error) {
                    console.error('Error loading sample data:', error);
                    sampleArea.innerHTML = '<div class="error">Error loading sample data</div>';
                }
            }

            async handleQueryExecute() {
                const question = document.getElementById('query-input').value.trim();
                const database = document.getElementById('database-select').value;
                const sessionId = this.currentConnection?.sessionId;
                const dbType = this.currentConnection?.type;

                if (!question || !database || !sessionId) {
                    this.showStatus('Missing input or session', 'error');
                    return;
                }

                this.showStatus('Generating query...', 'connecting');
                this.showPanel('results-panel');

                try {
                    const response = await fetch(`${this.apiBase}/ai-query`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            session_id: sessionId,
                            database,
                            question,
                            db_type: dbType,
                            execute: true
                        })
                    });

                    const data = await response.json();

                    if (!data.success) throw new Error(data.error || 'Query generation failed');

                    // 🌟 Display query + results
                    document.getElementById('query-info').innerHTML = `
                        <p><strong>Generated Query:</strong></p>
                        <code style="display:block; background:#111; padding:10px; margin-bottom:10px; color:#00ffcc;">${data.generated_query}</code>
                    `;

                    this.renderQueryResults(data.results);

                    this.showStatus(`Query executed successfully. ${data.count} rows returned.`, 'success');
                } catch (err) {
                    this.showStatus(`❌ ${err.message}`, 'error');
                }
            }

            displayError(message) {
                this.showStatus(`❌ ${message}`, 'error');
                
                // Also display in the results area for better visibility
                const resultsContainer = document.getElementById('query-results');
                resultsContainer.innerHTML = `
                    <div class="query-error">
                        <h4>Error</h4>
                        <p>${message}</p>
                        <div class="error-help">
                            <p><strong>Troubleshooting:</strong></p>
                            <ul>
                                <li>Check that your database connection is active</li>
                                <li>Verify that you've selected a database</li>
                                <li>Try refreshing the page and reconnecting</li>
                                <li>Check server logs for more details</li>
                            </ul>
                        </div>
                    </div>
                `;
            }

            displayQueryResults(data) {
                const resultsContainer = document.getElementById('query-results');
                
                // Clear previous results
                resultsContainer.innerHTML = '';
                
                // Display the generated query
                const queryDiv = document.createElement('div');
                queryDiv.className = 'generated-query';
                
                const queryHeader = document.createElement('h4');
                queryHeader.textContent = 'Generated Query';
                queryDiv.appendChild(queryHeader);
                
                const queryCode = document.createElement('pre');
                queryCode.className = 'query-code';
                queryCode.textContent = data.generated_query;
                queryDiv.appendChild(queryCode);
                
                resultsContainer.appendChild(queryDiv);
                
                // Add execution time if available
                if (data.execution_time) {
                    const timeInfo = document.createElement('div');
                    timeInfo.className = 'execution-time';
                    timeInfo.innerHTML = `<span>Execution time:</span> ${data.execution_time}s`;
                    resultsContainer.appendChild(timeInfo);
                }
                
                // Check if there was an execution error
                if (data.execution_error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'query-error';
                    errorDiv.innerHTML = `<h4>Error</h4><p>${data.execution_error}</p>`;
                    resultsContainer.appendChild(errorDiv);
                    return;
                }
                
                // Check if there are results
                if (!data.results || data.results.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.textContent = data.display?.empty_message || 'No results found';
                    resultsContainer.appendChild(noResults);
                    return;
                }
                
                // Create results table
                const table = document.createElement('table');
                table.className = 'results-table';
                
                // Create table header
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');
                
                // Get column names from the first result
                const columns = Object.keys(data.results[0]);
                
                columns.forEach(column => {
                    const th = document.createElement('th');
                    th.textContent = column;
                    
                    // Add data type as attribute for styling if available
                    if (data.display?.columns) {
                        const columnInfo = data.display.columns.find(col => col.name === column);
                        if (columnInfo) {
                            th.setAttribute('data-type', columnInfo.type);
                        }
                    }
                    
                    headerRow.appendChild(th);
                });
                
                thead.appendChild(headerRow);
                table.appendChild(thead);
                
                // Create table body
                const tbody = document.createElement('tbody');
                
                data.results.forEach(row => {
                    const tr = document.createElement('tr');
                    
                    columns.forEach(column => {
                        const td = document.createElement('td');
                        const value = row[column];
                        
                        // Format cell based on data type
                        if (value === null || value === undefined) {
                            td.innerHTML = '<em class="null-value">NULL</em>';
                            td.className = 'null-cell';
                        } else if (typeof value === 'object' && value !== null) {
                            // Format JSON data
                            td.innerHTML = `<code>${JSON.stringify(value, null, 2)}</code>`;
                            td.className = 'json-cell';
                        } else {
                            // Get column type if available
                            let columnType = 'string';
                            if (data.display?.columns) {
                                const columnInfo = data.display.columns.find(col => col.name === column);
                                if (columnInfo) {
                                    columnType = columnInfo.type;
                                }
                            }
                            
                            // Format based on type
                            if (columnType === 'number') {
                                td.textContent = value;
                                td.className = 'number-cell';
                                td.style.textAlign = 'right';
                            } else if (columnType === 'date') {
                                td.textContent = value;
                                td.className = 'date-cell';
                            } else if (columnType === 'boolean') {
                                td.textContent = value ? 'True' : 'False';
                                td.className = 'boolean-cell';
                            } else {
                                // Handle long text with truncation
                                const displayValue = String(value);
                                if (displayValue.length > 100) {
                                    td.textContent = displayValue.substring(0, 100) + '...';
                                    td.title = displayValue; // Show full text on hover
                                    td.className = 'text-cell truncated';
                                } else {
                                    td.textContent = displayValue;
                                    td.className = 'text-cell';
                                }
                            }
                        }
                        
                        tr.appendChild(td);
                    });
                    
                    tbody.appendChild(tr);
                });
                
                table.appendChild(tbody);
                
                // Add results info
                const resultsInfo = document.createElement('div');
                resultsInfo.className = 'results-info';
                
                if (data.display?.summary) {
                    resultsInfo.textContent = data.display.summary;
                } else {
                    resultsInfo.textContent = `Showing ${data.results.length} results`;
                }
                
                // Add export buttons
                const exportButtons = document.createElement('div');
                exportButtons.className = 'export-buttons';
                exportButtons.innerHTML = `
                    <button onclick="exportCSV()" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-file-csv"></i> Export CSV
                    </button>
                    <button onclick="exportJSON()" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-file-code"></i> Export JSON
                    </button>
                `;
                
                resultsInfo.appendChild(exportButtons);
                resultsContainer.appendChild(resultsInfo);
                resultsContainer.appendChild(table);
                
                // Add some styling for better readability
                const style = document.createElement('style');
                style.textContent = `
                    .results-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
                    .results-table th { background: #f5f5f5; padding: 8px; text-align: left; border-bottom: 2px solid #ddd; }
                    .results-table td { padding: 8px; border-bottom: 1px solid #eee; }
                    .results-table tr:hover { background-color: #f9f9f9; }
                    .number-cell { text-align: right; font-family: monospace; }
                    .date-cell { font-family: monospace; }
                    .null-cell { color: #999; }
                    .null-value { font-style: italic; color: #999; }
                    .json-cell { font-family: monospace; white-space: pre-wrap; }
                    .truncated { position: relative; cursor: help; }
                    .execution-time { margin: 10px 0; font-size: 0.9em; color: #666; }
                    .execution-time span { font-weight: bold; }
                    .export-buttons { float: right; }
                    .export-buttons button { margin-left: 5px; }
                    .query-error { margin: 15px 0; padding: 10px; background-color: #fff0f0; border-left: 4px solid #ff5252; }
                    .query-error h4 { margin-top: 0; color: #d32f2f; }
                    .no-results { padding: 20px; text-align: center; color: #666; font-style: italic; }
                    .generated-query { margin-bottom: 15px; }
                    .query-code { background: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto; }
                `;
                resultsContainer.appendChild(style);
            }

            displayResults(results, container, title = '') {
                if (!results || results.length === 0) {
                    container.innerHTML = '<div class="loading">No data to display</div>';
                    return;
                }

                const headers = Object.keys(results[0]);
                
                container.innerHTML = `
                    ${title ? `<h3>${title}</h3>` : ''}
                    <table class="results-table">
                        <thead>
                            <tr>
                                ${headers.map(header => `<th>${header}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${results.map(row => `
                                <tr>
                                    ${headers.map(header => {
                                        const value = row[header];
                                        const displayValue = value !== null && value !== undefined ? 
                                            String(value).substring(0, 100) + (String(value).length > 100 ? '...' : '') : 
                                            '<em>NULL</em>';
                                        return `<td>${displayValue}</td>`;
                                    }).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }

            handleClear() {
                document.getElementById('query-input').value = '';
                document.getElementById('query-results').innerHTML = '<div class="loading">Execute a query to see results</div>';
                document.getElementById('query-info').innerHTML = '';
            }

            // UI Helper Methods
            showStatus(message, type) {
                const statusText = document.getElementById('status-text');
                const statusDot = document.getElementById('status-dot');
                
                statusText.textContent = message;
                
                // Remove all status classes
                statusDot.classList.remove('connected', 'connecting');
                
                // Add appropriate class
                if (type === 'connected') {
                    statusDot.classList.add('connected');
                } else if (type === 'connecting') {
                    statusDot.classList.add('connecting');
                }
                
                // Update connection info
                if (this.currentConnection) {
                    document.getElementById('connection-info').innerHTML = `
                        <span style="font-size: 0.9rem; color: #888;">
                            ${this.currentConnection.type.toUpperCase()}
                            ${this.selectedDatabase ? ` • ${this.selectedDatabase}` : ''}
                        </span>
                    `;
                }
                
                console.log(`Status: ${message} (${type})`);
            }

            activateStep(stepNumber) {
                // Enable the step
                const stepElement = document.getElementById(`step-${stepNumber}`);
                stepElement.classList.remove('disabled');
                stepElement.classList.add('active');
                
                // Remove active from previous steps
                for (let i = 1; i < stepNumber; i++) {
                    const prevStep = document.getElementById(`step-${i}`);
                    if (prevStep.classList.contains('active')) {
                        prevStep.classList.remove('active');
                    }
                }
                
                this.currentStep = stepNumber;
                console.log(`Activated step ${stepNumber}`);
            }

            completeStep(stepNumber) {
                const stepElement = document.getElementById(`step-${stepNumber}`);
                stepElement.classList.remove('active');
                stepElement.classList.add('completed');
                console.log(`Completed step ${stepNumber}`);
            }

            showPanel(panelId) {
                // Hide all panels
                document.querySelectorAll('.content-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                
                // Show selected panel
                document.getElementById(panelId).classList.add('active');
                console.log(`Showing panel: ${panelId}`);
            }

            // Utility Methods
            async testConnection() {
                try {
                    const response = await fetch(`http://localhost:3002/test`);
                    if (response.ok) {
                        console.log('✅ Server connection test successful');
                        return true;
                    }
                } catch (error) {
                    console.error('❌ Server connection test failed:', error);
                    this.showStatus('Cannot connect to server. Please ensure Node.js server is running on port 3002.', 'error');
                    return false;
                }
            }

            // Export functionality
            exportResults(format) {
                const resultsTable = document.querySelector('#query-results .results-table');
                if (!resultsTable) {
                    this.showStatus('No results to export', 'error');
                    return;
                }

                const headers = Array.from(resultsTable.querySelectorAll('thead th')).map(th => th.textContent);
                const rows = Array.from(resultsTable.querySelectorAll('tbody tr')).map(tr => 
                    Array.from(tr.querySelectorAll('td')).map(td => td.textContent)
                );

                let content, filename, mimeType;

                if (format === 'csv') {
                    content = [
                        headers.join(','),
                        ...rows.map(row => row.map(cell => 
                            cell.includes(',') ? `"${cell.replace(/"/g, '""')}"` : cell
                        ).join(','))
                    ].join('\n');
                    filename = `query_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
                    mimeType = 'text/csv';
                } else if (format === 'json') {
                    const jsonData = rows.map(row => {
                        const obj = {};
                        headers.forEach((header, index) => {
                            obj[header] = row[index];
                        });
                        return obj;
                    });
                    content = JSON.stringify(jsonData, null, 2);
                    filename = `query_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    mimeType = 'application/json';
                }

                const blob = new Blob([content], { type: mimeType });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showStatus(`Exported results as ${format.toUpperCase()}`, 'connected');
            }

            // Reset application
            reset() {
                this.currentConnection = null;
                this.selectedDatabase = null;
                this.schema = null;
                this.currentStep = 1;

                // Reset UI
                document.getElementById('db-type').value = '';
                document.getElementById('connection-fields').innerHTML = '';
                document.getElementById('database-select').innerHTML = '<option value="">Select database...</option>';
                document.getElementById('query-input').value = '';
                document.getElementById('query-results').innerHTML = '<div class="loading">Execute a query to see results</div>';
                document.getElementById('query-info').innerHTML = '';

                // Reset steps
                for (let i = 1; i <= 4; i++) {
                    const step = document.getElementById(`step-${i}`);
                    step.classList.remove('active', 'completed');
                    if (i > 1) step.classList.add('disabled');
                }
                document.getElementById('step-1').classList.add('active');

                // Show welcome panel
                this.showPanel('welcome-panel');
                this.showStatus('Ready to connect', 'ready');
            }

            async debugConnection() {
                console.log('🔍 Starting connection debug...');
                
                // Test 1: Server reachability
                try {
                    const testResponse = await fetch('http://localhost:3002/test');
                    const testData = await testResponse.json();
                    console.log('✅ Test endpoint works:', testData);
                } catch (error) {
                    console.error('❌ Test endpoint failed:', error);
                    return;
                }
                
                // Test 2: Check if backend is running
                try {
                    const healthResponse = await fetch('http2://localhost:3002/health');
                    const healthData = await healthResponse.json();
                    console.log('✅ Health check works:', healthData);
                } catch (error) {
                    console.error('❌ Health check failed:', error);
                }
                
                // Test 3: Try the actual connection endpoint
                try {
                    const connectResponse = await fetch('http://localhost:3002/api/database/connect', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            type: 'mysql',
                            database: 'test',
                            config: {
                                host: 'localhost',
                                port: 3306,
                                username: 'root',
                                password: 'test'
                            }
                        })
                    });
                    
                    console.log('Connect response status:', connectResponse.status);
                    console.log('Connect response headers:', [...connectResponse.headers.entries()]);
                    
                    const responseText = await connectResponse.text();
                    console.log('Connect response body:', responseText);
                    
                    try {
                        const connectData = JSON.parse(responseText);
                        console.log('✅ Connect endpoint works:', connectData);
                    } catch (parseError) {
                        console.error('❌ Connect endpoint returned non-JSON:', responseText);
                    }
                } catch (error) {
                    console.error('❌ Connect endpoint failed:', error);
                }
            }
        renderQueryResults(results, generatedQuery = '') {
            const resultsContainer = document.getElementById('query-results');
            let html = '';

            if (generatedQuery) {
                html += `<div class="success">Generated SQL: <code>${generatedQuery}</code></div>`;
            }

            if (!Array.isArray(results) || results.length === 0) {
                html += `<div class="error">No results found.</div>`;
                resultsContainer.innerHTML = html;
                this.showPanel('results-panel');
                return;
            }

            const headers = Object.keys(results[0]);
            html += `
                <table class="results-table">
                    <thead>
                        <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
                    </thead>
                    <tbody>
                        ${results.map(row => `
                            <tr>${headers.map(h => `<td>${row[h]}</td>`).join('')}</tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            resultsContainer.innerHTML = html;
            this.showPanel('results-panel');
        }

        copySQLToClipboard() {
            const codeElement = document.querySelector('#query-results .success code');
            if (!codeElement) {
                this.showStatus('No SQL query to copy', 'error');
                return;
            }
            const sqlText = codeElement.textContent;
            navigator.clipboard.writeText(sqlText).then(() => {
                this.showStatus('SQL query copied to clipboard', 'connected');
            }).catch(err => {
                console.error('Failed to copy SQL:', err);
                this.showStatus('Failed to copy SQL query', 'error');
            });
        }
    }

    // Initialize the application
    let app;
    document.addEventListener('DOMContentLoaded', () => {
        app = new SQLChatApp();

        // Add global functions for buttons
        window.exportCSV = () => app.exportResults('csv');
        window.exportJSON = () => app.exportResults('json');
        window.resetApp = () => app.reset();

        console.log('SQL Chat App loaded successfully');
    });

    // Enhanced parallax effect with performance optimization
    let ticking = false;
    let mouseX = 0;
    let mouseY = 0;

    function updateParallax() {
        const x = (mouseX / window.innerWidth - 0.5) * 30;
        const y = (mouseY / window.innerHeight - 0.5) * 30;
        const parallaxBg = document.querySelector('.parallax-bg');

        if (parallaxBg) {
            parallaxBg.style.transform = `translate3d(${x}px, ${y}px, 0)`;
            parallaxBg.style.backgroundPosition = `${50 + x * 0.5}% ${50 + y * 0.5}%`;
        }

        ticking = false;
    }

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    });

    // Add floating animation to elements
    document.addEventListener('DOMContentLoaded', () => {
        const floatingElements = document.querySelectorAll('.step-section, .table-item');
        floatingElements.forEach((el, index) => {
            el.style.animationDelay = `${index * 0.1}s`;
            el.style.animation = 'float 6s ease-in-out infinite';
        });
    });

    // Smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Performance optimization for high refresh rate displays
    if (window.matchMedia('(min-resolution: 120dpi)').matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.15s');
    }

    if (window.matchMedia('(min-resolution: 144dpi)').matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.1s');
    }
</script>
<style>
.success code {
    background: rgba(80, 200, 120, 0.1);
    padding: 6px 12px;
    border-radius: 6px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    border: 1px solid rgba(80, 200, 120, 0.2);
}

/* Additional visual enhancements */
.loading {
    position: relative;
    color: var(--text-secondary);
}

.loading::after {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid var(--primary-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Smooth transitions for all interactive elements */
.form-control, .btn, .step-section, .table-item {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Focus styles for accessibility */
.btn:focus, .form-control:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-medium);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
</style>
</body>
</html>
