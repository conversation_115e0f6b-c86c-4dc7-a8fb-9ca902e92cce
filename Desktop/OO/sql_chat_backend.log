2025-06-20 01:06:55,907 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:06:55,910 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:15:58,876 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 318f8d92-ce3b-4727-9ae5-9d7fc61cb684
2025-06-20 01:15:58,882 - __main__ - ERROR - [m.py:795] - Connection error: name 'sql_manager' is not defined
2025-06-20 01:15:59,009 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:15:59] "[35m[1mPOST /api/connect HTTP/1.1[0m" 500 -
2025-06-20 01:17:44,551 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:19:13,582 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***************:5001
2025-06-20 01:19:13,582 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-20 01:19:13,584 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:19:13,988 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:19:13,989 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:19:21,456 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session ef033e14-2772-4e28-8645-a5ebfd38954e
2025-06-20 01:19:21,573 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:19:21,574 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:19:21,576 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:19:21,594 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:19:21,595 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:19:21,599 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:19:21,650 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session ef033e14-2772-4e28-8645-a5ebfd38954e
2025-06-20 01:19:21,652 - __main__ - INFO - [m.py:410] - Connection stored for session ef033e14-2772-4e28-8645-a5ebfd38954e: mysql/
2025-06-20 01:19:21,655 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:21] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:19:21,832 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:21] "[33mGET /api/databases?session_id=ef033e14-2772-4e28-8645-a5ebfd38954e HTTP/1.1[0m" 404 -
2025-06-20 01:19:40,937 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903
2025-06-20 01:19:40,944 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903
2025-06-20 01:19:40,945 - __main__ - INFO - [m.py:410] - Connection stored for session b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903: mysql/
2025-06-20 01:19:40,947 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:40] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:19:41,151 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:19:41] "[33mGET /api/databases?session_id=b9ddf9c3-004b-4e4f-bdc7-ecb8beec2903 HTTP/1.1[0m" 404 -
2025-06-20 01:32:32,749 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:32:33,892 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:32:35,196 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:32:35,199 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:32:41,061 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session a7e8cdb0-d089-4df9-b787-96463aa027fd
2025-06-20 01:32:41,209 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:32:41,209 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:32:41,209 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:32:41,212 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:32:41,212 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:32:41,213 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:32:41,232 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session a7e8cdb0-d089-4df9-b787-96463aa027fd
2025-06-20 01:32:41,232 - __main__ - INFO - [m.py:410] - Connection stored for session a7e8cdb0-d089-4df9-b787-96463aa027fd: mysql/
2025-06-20 01:32:41,233 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:41] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:32:41,585 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:41] "GET /api/databases?session_id=a7e8cdb0-d089-4df9-b787-96463aa027fd HTTP/1.1" 200 -
2025-06-20 01:32:43,150 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session b237dc5e-f706-4a00-8dfb-7ef8e0bae74d
2025-06-20 01:32:43,153 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session b237dc5e-f706-4a00-8dfb-7ef8e0bae74d
2025-06-20 01:32:43,154 - __main__ - INFO - [m.py:410] - Connection stored for session b237dc5e-f706-4a00-8dfb-7ef8e0bae74d: mysql/
2025-06-20 01:32:43,154 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:43] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:32:43,249 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:43] "GET /api/databases?session_id=b237dc5e-f706-4a00-8dfb-7ef8e0bae74d HTTP/1.1" 200 -
2025-06-20 01:32:51,906 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session af68093d-5030-4575-be92-e1a2a728ba4b
2025-06-20 01:32:51,912 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session af68093d-5030-4575-be92-e1a2a728ba4b
2025-06-20 01:32:51,913 - __main__ - INFO - [m.py:410] - Connection stored for session af68093d-5030-4575-be92-e1a2a728ba4b: mysql/
2025-06-20 01:32:51,915 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:51] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:32:52,038 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:32:52] "GET /api/databases?session_id=af68093d-5030-4575-be92-e1a2a728ba4b HTTP/1.1" 200 -
2025-06-20 01:34:01,862 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session f2a46d9d-1c1a-4a47-8f00-d347321b7464
2025-06-20 01:34:01,870 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session f2a46d9d-1c1a-4a47-8f00-d347321b7464
2025-06-20 01:34:01,871 - __main__ - INFO - [m.py:410] - Connection stored for session f2a46d9d-1c1a-4a47-8f00-d347321b7464: mysql/
2025-06-20 01:34:01,882 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:34:01] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:34:02,113 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:34:02] "GET /api/databases?session_id=f2a46d9d-1c1a-4a47-8f00-d347321b7464 HTTP/1.1" 200 -
2025-06-20 01:35:33,959 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session 5ec224c3-6b1b-4a7b-8668-fb87fb7f5e12
2025-06-20 01:35:33,978 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-20 01:35:33,981 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-19T20:05:33.980929+00:00', 'session_id': '5ec224c3-6b1b-4a7b-8668-fb87fb7f5e12', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:35:33,983 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:35:33] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:36:12,578 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session e519001a-32a5-492d-9c18-2585f2d84096
2025-06-20 01:36:12,583 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-20 01:36:12,587 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-19T20:06:12.586580+00:00', 'session_id': 'e519001a-32a5-492d-9c18-2585f2d84096', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:36:12,590 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:36:12] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:36:52,165 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session 8f535ac8-b578-42c3-9b29-8d584dc37b4c
2025-06-20 01:36:52,426 - __main__ - ERROR - [m.py:285] - Connection error: Authentication failed., full error: {'ok': 0.0, 'errmsg': 'Authentication failed.', 'code': 18, 'codeName': 'AuthenticationFailed'}
2025-06-20 01:36:52,430 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': "Authentication failed., full error: {'ok': 0.0, 'errmsg': 'Authentication failed.', 'code': 18, 'codeName': 'AuthenticationFailed'}", 'timestamp': '2025-06-19T20:06:52.430552+00:00', 'session_id': '8f535ac8-b578-42c3-9b29-8d584dc37b4c', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:36:52,433 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:36:52] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:37:06,398 - __main__ - INFO - [m.py:766] - Attempting to connect to mongodb database for session a958f1c5-77d2-40c4-847f-101439004a9a
2025-06-20 01:37:06,399 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-20 01:37:06,399 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-19T20:07:06.399765+00:00', 'session_id': 'a958f1c5-77d2-40c4-847f-101439004a9a', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-20 01:37:06,401 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:37:06] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-20 01:39:32,999 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:39:34,051 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:39:35,996 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:39:35,998 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:39:43,749 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 5ba013d1-4901-4543-b7a8-b233e896b94b
2025-06-20 01:39:43,839 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:39:43,840 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:39:43,841 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:39:43,846 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:39:43,847 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:39:43,849 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:39:43,875 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5ba013d1-4901-4543-b7a8-b233e896b94b
2025-06-20 01:39:43,876 - __main__ - INFO - [m.py:410] - Connection stored for session 5ba013d1-4901-4543-b7a8-b233e896b94b: mysql/
2025-06-20 01:39:43,880 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:39:43] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:39:44,102 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:39:44] "GET /api/databases?session_id=5ba013d1-4901-4543-b7a8-b233e896b94b HTTP/1.1" 200 -
2025-06-20 01:41:30,126 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:41:30,297 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:41:31,322 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:41:31,323 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:41:32,379 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-20 01:41:32,480 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-20 01:41:33,132 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-20 01:41:33,133 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 445-555-931
2025-06-20 01:41:48,813 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session f100c3bc-1242-4263-80f0-6bb2e1a77340
2025-06-20 01:41:48,864 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:41:48,865 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-20 01:41:48,866 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-20 01:41:48,871 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-20 01:41:48,873 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-20 01:41:48,878 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-20 01:41:48,917 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session f100c3bc-1242-4263-80f0-6bb2e1a77340
2025-06-20 01:41:48,919 - __main__ - INFO - [m.py:410] - Connection stored for session f100c3bc-1242-4263-80f0-6bb2e1a77340: mysql/
2025-06-20 01:41:48,927 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:41:48] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:41:49,163 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:41:49] "GET /api/databases?session_id=f100c3bc-1242-4263-80f0-6bb2e1a77340 HTTP/1.1" 200 -
2025-06-20 01:53:10,224 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 29efdf6c-5a26-4f7b-97c2-87820c1552d1
2025-06-20 01:53:10,342 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 29efdf6c-5a26-4f7b-97c2-87820c1552d1
2025-06-20 01:53:10,345 - __main__ - INFO - [m.py:410] - Connection stored for session 29efdf6c-5a26-4f7b-97c2-87820c1552d1: mysql/
2025-06-20 01:53:10,364 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:53:10] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:53:10,726 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:53:10] "GET /api/databases?session_id=29efdf6c-5a26-4f7b-97c2-87820c1552d1 HTTP/1.1" 200 -
2025-06-20 01:55:56,300 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 70278f36-b341-4eb8-ac60-8526a95b80a2
2025-06-20 01:55:56,386 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 70278f36-b341-4eb8-ac60-8526a95b80a2
2025-06-20 01:55:56,389 - __main__ - INFO - [m.py:410] - Connection stored for session 70278f36-b341-4eb8-ac60-8526a95b80a2: mysql/
2025-06-20 01:55:56,411 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:55:56] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:55:56,632 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:55:56] "GET /api/databases?session_id=70278f36-b341-4eb8-ac60-8526a95b80a2 HTTP/1.1" 200 -
2025-06-20 01:55:58,784 - __main__ - INFO - [m.py:486] - Updated selected database for session 70278f36-b341-4eb8-ac60-8526a95b80a2: us_data
2025-06-20 01:55:58,784 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:55:58] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 01:58:45,721 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session c0544786-85ec-4f7b-ae92-96140d0eeab6
2025-06-20 01:58:45,829 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session c0544786-85ec-4f7b-ae92-96140d0eeab6
2025-06-20 01:58:45,832 - __main__ - INFO - [m.py:410] - Connection stored for session c0544786-85ec-4f7b-ae92-96140d0eeab6: mysql/
2025-06-20 01:58:45,836 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:58:45] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:58:46,045 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:58:46] "GET /api/databases?session_id=c0544786-85ec-4f7b-ae92-96140d0eeab6 HTTP/1.1" 200 -
2025-06-20 01:58:48,730 - __main__ - INFO - [m.py:486] - Updated selected database for session c0544786-85ec-4f7b-ae92-96140d0eeab6: us_data
2025-06-20 01:58:48,732 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:58:48] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 01:59:32,355 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session aaa566fb-9c99-459a-8253-09e3f3b5951e
2025-06-20 01:59:32,364 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session aaa566fb-9c99-459a-8253-09e3f3b5951e
2025-06-20 01:59:32,365 - __main__ - INFO - [m.py:410] - Connection stored for session aaa566fb-9c99-459a-8253-09e3f3b5951e: mysql/
2025-06-20 01:59:32,369 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:32] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 01:59:32,569 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:32] "GET /api/databases?session_id=aaa566fb-9c99-459a-8253-09e3f3b5951e HTTP/1.1" 200 -
2025-06-20 01:59:35,840 - __main__ - INFO - [m.py:486] - Updated selected database for session aaa566fb-9c99-459a-8253-09e3f3b5951e: us_data
2025-06-20 01:59:35,841 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:35] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 01:59:35,927 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 01:59:35] "[31m[1mGET /api/schema?session_id=aaa566fb-9c99-459a-8253-09e3f3b5951e HTTP/1.1[0m" 400 -
2025-06-20 02:03:53,111 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 1b809611-0d71-4293-8e15-16a28d312a1a
2025-06-20 02:03:53,170 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 1b809611-0d71-4293-8e15-16a28d312a1a
2025-06-20 02:03:53,172 - __main__ - INFO - [m.py:410] - Connection stored for session 1b809611-0d71-4293-8e15-16a28d312a1a: mysql/
2025-06-20 02:03:53,178 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:53] "POST /api/connect HTTP/1.1" 200 -
2025-06-20 02:03:53,476 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:53] "GET /api/databases?session_id=1b809611-0d71-4293-8e15-16a28d312a1a HTTP/1.1" 200 -
2025-06-20 02:03:57,271 - __main__ - INFO - [m.py:486] - Updated selected database for session 1b809611-0d71-4293-8e15-16a28d312a1a: us_data
2025-06-20 02:03:57,273 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:57] "POST /api/select-database HTTP/1.1" 200 -
2025-06-20 02:03:57,676 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [20/Jun/2025 02:03:57] "GET /api/schema?session_id=1b809611-0d71-4293-8e15-16a28d312a1a&database=us_data HTTP/1.1" 200 -
2025-06-27 12:55:10,569 - __main__ - INFO - GROQ API key loaded successfully (starts with: gsk_ufde7D...)
2025-06-27 12:55:10,700 - __main__ - INFO - GROQ API client initialized successfully with key: gsk_ufde7D...
2025-06-27 12:55:11,028 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-27 12:55:11,032 - __main__ - INFO - GROQ API key validation successful
2025-06-27 12:55:11,076 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 12:55:11,076 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 12:55:11,081 - werkzeug - INFO -  * Restarting with stat
2025-06-27 12:55:11,645 - __main__ - INFO - GROQ API key loaded successfully (starts with: gsk_ufde7D...)
2025-06-27 12:55:11,699 - __main__ - INFO - GROQ API client initialized successfully with key: gsk_ufde7D...
2025-06-27 12:55:11,982 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-27 12:55:11,987 - __main__ - INFO - GROQ API key validation successful
2025-06-27 12:55:12,031 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 12:55:12,179 - werkzeug - INFO -  * Debugger PIN: 561-057-362
2025-06-27 12:55:56,956 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 12:55:56,956 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 12:55:56,957 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 12:55:57,422 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 12:55:57,423 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 12:56:31,085 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348
2025-06-27 12:56:31,216 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 12:56:31,216 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 12:56:31,216 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 12:56:31,230 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 12:56:31,231 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 12:56:31,236 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 12:56:31,292 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348
2025-06-27 12:56:31,293 - __main__ - INFO - [m.py:410] - Connection stored for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348: mysql/
2025-06-27 12:56:31,298 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:31] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 12:56:31,611 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:31] "GET /api/databases?session_id=63ee9dd7-8392-4c21-be72-2c9fdff1e348 HTTP/1.1" 200 -
2025-06-27 12:56:35,345 - __main__ - INFO - [m.py:486] - Updated selected database for session 63ee9dd7-8392-4c21-be72-2c9fdff1e348: us_data
2025-06-27 12:56:35,346 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:35] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 12:56:35,490 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:56:35] "GET /api/schema?session_id=63ee9dd7-8392-4c21-be72-2c9fdff1e348&database=us_data HTTP/1.1" 200 -
2025-06-27 12:57:21,826 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6
2025-06-27 12:57:21,830 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6
2025-06-27 12:57:21,831 - __main__ - INFO - [m.py:410] - Connection stored for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6: mysql/
2025-06-27 12:57:21,834 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:21] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 12:57:21,921 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:21] "GET /api/databases?session_id=5e25faff-98b9-40b1-85d8-aed3a1cfb6e6 HTTP/1.1" 200 -
2025-06-27 12:57:25,709 - __main__ - INFO - [m.py:486] - Updated selected database for session 5e25faff-98b9-40b1-85d8-aed3a1cfb6e6: us_data
2025-06-27 12:57:25,710 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:25] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 12:57:25,816 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 12:57:25] "GET /api/schema?session_id=5e25faff-98b9-40b1-85d8-aed3a1cfb6e6&database=us_data HTTP/1.1" 200 -
2025-06-27 14:02:38,877 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 14:02:38,878 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 14:02:38,879 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 14:02:39,356 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 14:02:39,356 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 14:02:57,642 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session e9d83772-a60f-43ed-bb73-2dc80662f0d1
2025-06-27 14:02:57,817 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:02:57,818 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 14:02:57,818 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 14:02:57,826 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:02:57,826 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 14:02:57,828 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 14:02:57,872 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e9d83772-a60f-43ed-bb73-2dc80662f0d1
2025-06-27 14:02:57,873 - __main__ - INFO - [m.py:410] - Connection stored for session e9d83772-a60f-43ed-bb73-2dc80662f0d1: mysql/
2025-06-27 14:02:57,876 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:02:57] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:02:58,077 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:02:58] "GET /api/databases?session_id=e9d83772-a60f-43ed-bb73-2dc80662f0d1 HTTP/1.1" 200 -
2025-06-27 14:03:00,812 - __main__ - INFO - [m.py:486] - Updated selected database for session e9d83772-a60f-43ed-bb73-2dc80662f0d1: us_data
2025-06-27 14:03:00,813 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:03:00] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:03:01,036 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:03:01] "GET /api/schema?session_id=e9d83772-a60f-43ed-bb73-2dc80662f0d1&database=us_data HTTP/1.1" 200 -
2025-06-27 14:26:28,592 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa
2025-06-27 14:26:28,741 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa
2025-06-27 14:26:28,744 - __main__ - INFO - [m.py:410] - Connection stored for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa: mysql/
2025-06-27 14:26:28,772 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:28] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:26:29,116 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:29] "GET /api/databases?session_id=df51e1c3-eedf-40c9-895d-58cf0ecd1ffa HTTP/1.1" 200 -
2025-06-27 14:26:34,321 - __main__ - INFO - [m.py:486] - Updated selected database for session df51e1c3-eedf-40c9-895d-58cf0ecd1ffa: us_data
2025-06-27 14:26:34,324 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:34] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:26:34,497 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:34] "GET /api/tables?session_id=df51e1c3-eedf-40c9-895d-58cf0ecd1ffa&database=us_data HTTP/1.1" 200 -
2025-06-27 14:26:34,580 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:26:34] "GET /api/schema?session_id=df51e1c3-eedf-40c9-895d-58cf0ecd1ffa&database=us_data HTTP/1.1" 200 -
2025-06-27 14:28:34,635 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 14:28:35,752 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 14:28:36,635 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 14:28:36,636 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 14:30:51,225 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a
2025-06-27 14:30:51,336 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:30:51,337 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 14:30:51,337 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 14:30:51,341 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:30:51,341 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 14:30:51,351 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 14:30:51,377 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a
2025-06-27 14:30:51,380 - __main__ - INFO - [m.py:410] - Connection stored for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a: mysql/
2025-06-27 14:30:51,396 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:51] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:30:51,548 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:51] "GET /api/databases?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a HTTP/1.1" 200 -
2025-06-27 14:30:56,643 - __main__ - INFO - [m.py:486] - Updated selected database for session 6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a: us_data
2025-06-27 14:30:56,645 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:56] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:30:56,773 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:56] "GET /api/tables?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a&database=us_data HTTP/1.1" 200 -
2025-06-27 14:30:56,898 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:30:56] "GET /api/schema?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a&database=us_data HTTP/1.1" 200 -
2025-06-27 14:34:44,975 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 86d27621-4744-4d06-b727-0b033d3562c9
2025-06-27 14:34:45,038 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 86d27621-4744-4d06-b727-0b033d3562c9
2025-06-27 14:34:45,040 - __main__ - INFO - [m.py:410] - Connection stored for session 86d27621-4744-4d06-b727-0b033d3562c9: mysql/
2025-06-27 14:34:45,056 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:45] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:34:45,359 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:45] "GET /api/databases?session_id=86d27621-4744-4d06-b727-0b033d3562c9 HTTP/1.1" 200 -
2025-06-27 14:34:48,113 - __main__ - INFO - [m.py:486] - Updated selected database for session 86d27621-4744-4d06-b727-0b033d3562c9: us_data
2025-06-27 14:34:48,114 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:48] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:34:48,209 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:48] "GET /api/tables?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:34:48,294 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:34:48] "GET /api/schema?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:35:49,004 - __main__ - INFO - [m.py:486] - Updated selected database for session 86d27621-4744-4d06-b727-0b033d3562c9: me
2025-06-27 14:35:49,006 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:35:49] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:35:49,095 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:35:49] "GET /api/tables?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=me HTTP/1.1" 200 -
2025-06-27 14:35:49,225 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:35:49] "GET /api/schema?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=me HTTP/1.1" 200 -
2025-06-27 14:38:15,612 - __main__ - INFO - [m.py:486] - Updated selected database for session 86d27621-4744-4d06-b727-0b033d3562c9: us_data
2025-06-27 14:38:15,618 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:38:15] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:38:15,743 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:38:15] "GET /api/tables?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:38:15,886 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:38:15] "GET /api/schema?session_id=86d27621-4744-4d06-b727-0b033d3562c9&database=us_data HTTP/1.1" 200 -
2025-06-27 14:40:58,741 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:40:58] "GET /api/table-data?session_id=6f0bc7ab-04c2-41d8-86ad-e9caee3bc22a&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 14:42:58,550 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 14:42:58,551 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 14:42:58,553 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 14:42:58,983 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 14:42:58,984 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 14:43:30,075 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session cb09f43c-ee25-4369-904d-56cef8eeb077
2025-06-27 14:43:30,162 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:43:30,163 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 14:43:30,164 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 14:43:30,174 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 14:43:30,175 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 14:43:30,180 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 14:43:30,211 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session cb09f43c-ee25-4369-904d-56cef8eeb077
2025-06-27 14:43:30,211 - __main__ - INFO - [m.py:410] - Connection stored for session cb09f43c-ee25-4369-904d-56cef8eeb077: mysql/
2025-06-27 14:43:30,215 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:30] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 14:43:30,440 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:30] "GET /api/databases?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077 HTTP/1.1" 200 -
2025-06-27 14:43:33,917 - __main__ - INFO - [m.py:486] - Updated selected database for session cb09f43c-ee25-4369-904d-56cef8eeb077: us_data
2025-06-27 14:43:33,918 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:33] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 14:43:33,996 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:33] "GET /api/tables?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&database=us_data HTTP/1.1" 200 -
2025-06-27 14:43:34,107 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:43:34] "GET /api/schema?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&database=us_data HTTP/1.1" 200 -
2025-06-27 14:44:48,320 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:48] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 14:44:49,658 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:49] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 14:44:50,558 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:50] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=ji&limit=5 HTTP/1.1" 200 -
2025-06-27 14:44:51,458 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:44:51] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 14:45:08,057 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:45:08] "GET /api/table-data?session_id=cb09f43c-ee25-4369-904d-56cef8eeb077&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 14:52:44,011 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 14:52:44] "[33mPOST /api/ai-query HTTP/1.1[0m" 404 -
2025-06-27 15:05:39,721 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session b6df9260-e1da-44fe-a505-239873b2b9a6
2025-06-27 15:05:39,955 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session b6df9260-e1da-44fe-a505-239873b2b9a6
2025-06-27 15:05:39,958 - __main__ - INFO - [m.py:410] - Connection stored for session b6df9260-e1da-44fe-a505-239873b2b9a6: mysql/
2025-06-27 15:05:39,975 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:39] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:05:40,462 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:40] "GET /api/databases?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6 HTTP/1.1" 200 -
2025-06-27 15:05:42,428 - __main__ - INFO - [m.py:486] - Updated selected database for session b6df9260-e1da-44fe-a505-239873b2b9a6: us_data
2025-06-27 15:05:42,429 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:42] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:05:42,570 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:42] "GET /api/tables?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6&database=us_data HTTP/1.1" 200 -
2025-06-27 15:05:42,669 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:42] "GET /api/schema?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6&database=us_data HTTP/1.1" 200 -
2025-06-27 15:05:46,985 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:05:46] "GET /api/table-data?session_id=b6df9260-e1da-44fe-a505-239873b2b9a6&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 15:06:02,115 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:06:02] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:08:04,454 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:08:06,887 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:08:08,616 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:08:08,617 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:08:10,685 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:08:10,830 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:08:11,327 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:08:11,328 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:08:31,508 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session e11e9596-699e-4065-a664-08321397ae16
2025-06-27 15:08:31,612 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:08:31,613 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 15:08:31,614 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 15:08:31,621 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:08:31,623 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 15:08:31,631 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 15:08:31,685 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e11e9596-699e-4065-a664-08321397ae16
2025-06-27 15:08:31,687 - __main__ - INFO - [m.py:410] - Connection stored for session e11e9596-699e-4065-a664-08321397ae16: mysql/
2025-06-27 15:08:31,690 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:31] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:08:31,922 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:31] "GET /api/databases?session_id=e11e9596-699e-4065-a664-08321397ae16 HTTP/1.1" 200 -
2025-06-27 15:08:34,772 - __main__ - INFO - [m.py:486] - Updated selected database for session e11e9596-699e-4065-a664-08321397ae16: us_data
2025-06-27 15:08:34,773 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:34] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:08:34,931 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:34] "GET /api/tables?session_id=e11e9596-699e-4065-a664-08321397ae16&database=us_data HTTP/1.1" 200 -
2025-06-27 15:08:35,076 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:35] "GET /api/schema?session_id=e11e9596-699e-4065-a664-08321397ae16&database=us_data HTTP/1.1" 200 -
2025-06-27 15:08:47,492 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:08:47] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:11:20,209 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:11:20,981 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:11:22,733 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:11:22,739 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:11:34,940 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:11:35,077 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:11:35,946 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:11:35,948 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 15:12:26,218 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session da8fc6ae-19a8-475b-8c66-89d102804d84
2025-06-27 15:12:26,305 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:12:26,305 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 15:12:26,309 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 15:12:26,315 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 15:12:26,316 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 15:12:26,321 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 15:12:26,356 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session da8fc6ae-19a8-475b-8c66-89d102804d84
2025-06-27 15:12:26,359 - __main__ - INFO - [m.py:410] - Connection stored for session da8fc6ae-19a8-475b-8c66-89d102804d84: mysql/
2025-06-27 15:12:26,361 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:26] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:12:26,586 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:26] "GET /api/databases?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84 HTTP/1.1" 200 -
2025-06-27 15:12:28,952 - __main__ - INFO - [m.py:486] - Updated selected database for session da8fc6ae-19a8-475b-8c66-89d102804d84: us_data
2025-06-27 15:12:28,954 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:28] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:12:29,063 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:29] "GET /api/tables?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84&database=us_data HTTP/1.1" 200 -
2025-06-27 15:12:29,173 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:29] "GET /api/schema?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84&database=us_data HTTP/1.1" 200 -
2025-06-27 15:12:44,325 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:44] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:12:48,283 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:12:48] "GET /api/table-data?session_id=da8fc6ae-19a8-475b-8c66-89d102804d84&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 15:16:15,106 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session c3429d36-341c-41fd-8dc3-8bd79d1946db
2025-06-27 15:16:15,142 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session c3429d36-341c-41fd-8dc3-8bd79d1946db
2025-06-27 15:16:15,145 - __main__ - INFO - [m.py:410] - Connection stored for session c3429d36-341c-41fd-8dc3-8bd79d1946db: mysql/
2025-06-27 15:16:15,147 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:15] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 15:16:15,381 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:15] "GET /api/databases?session_id=c3429d36-341c-41fd-8dc3-8bd79d1946db HTTP/1.1" 200 -
2025-06-27 15:16:18,021 - __main__ - INFO - [m.py:486] - Updated selected database for session c3429d36-341c-41fd-8dc3-8bd79d1946db: us_data
2025-06-27 15:16:18,022 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:18] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 15:16:18,103 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:18] "GET /api/tables?session_id=c3429d36-341c-41fd-8dc3-8bd79d1946db&database=us_data HTTP/1.1" 200 -
2025-06-27 15:16:18,179 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:18] "GET /api/schema?session_id=c3429d36-341c-41fd-8dc3-8bd79d1946db&database=us_data HTTP/1.1" 200 -
2025-06-27 15:16:31,445 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 15:16:31] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 15:19:42,490 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 15:19:43,922 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 15:19:45,084 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 15:19:45,084 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 561-057-362
2025-06-27 17:08:45,591 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 17:08:45,830 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 17:08:45,832 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 17:08:45,838 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 17:08:45,848 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 17:08:45,849 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 17:08:45,887 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 17:08:45,944 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 17:08:45,946 - __main__ - INFO - [m.py:410] - Connection stored for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a: mysql/
2025-06-27 17:08:45,972 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:45] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 17:08:46,185 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:46] "GET /api/databases?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a HTTP/1.1" 200 -
2025-06-27 17:08:49,965 - __main__ - INFO - [m.py:486] - Updated selected database for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a: us_data
2025-06-27 17:08:49,966 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:49] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 17:08:50,127 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:50] "GET /api/tables?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a&database=us_data HTTP/1.1" 200 -
2025-06-27 17:08:50,240 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:50] "GET /api/schema?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a&database=us_data HTTP/1.1" 200 -
2025-06-27 17:08:57,231 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:08:57] "GET /api/table-data?session_id=5c2497a0-21cb-45db-ab9c-c6f771730a2a&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 17:19:04,466 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 17:19:04] "[33mPOST /api/generate-query HTTP/1.1[0m" 404 -
2025-06-27 18:10:14,943 - __main__ - INFO - [m.py:439] - Cleaning up expired session: 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 18:10:14,975 - __main__ - INFO - [m.py:478] - Connection removed for session 5c2497a0-21cb-45db-ab9c-c6f771730a2a
2025-06-27 22:25:03,825 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-27 22:25:03,826 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-27 22:25:03,827 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 22:25:04,279 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 22:25:04,374 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-27 22:25:24,729 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 22:25:24,964 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 22:25:24,964 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 22:25:24,964 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 22:25:24,974 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 22:25:24,975 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 22:25:24,976 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 22:25:25,021 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 22:25:25,021 - __main__ - INFO - [m.py:410] - Connection stored for session e62596e8-f899-42d1-9125-301caa4893bd: mysql/
2025-06-27 22:25:25,023 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:25] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 22:25:25,389 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:25] "GET /api/databases?session_id=e62596e8-f899-42d1-9125-301caa4893bd HTTP/1.1" 200 -
2025-06-27 22:25:27,738 - __main__ - INFO - [m.py:486] - Updated selected database for session e62596e8-f899-42d1-9125-301caa4893bd: us_data
2025-06-27 22:25:27,739 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:27] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 22:25:27,814 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:27] "GET /api/tables?session_id=e62596e8-f899-42d1-9125-301caa4893bd&database=us_data HTTP/1.1" 200 -
2025-06-27 22:25:28,000 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:25:27] "GET /api/schema?session_id=e62596e8-f899-42d1-9125-301caa4893bd&database=us_data HTTP/1.1" 200 -
2025-06-27 22:26:06,414 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:06] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:26:09,291 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:09] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:12,204 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:12] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:26:20,241 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:20] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=ji&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:22,652 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:22] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=employees&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:24,038 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:24] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 22:26:31,950 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:31] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:26:41,864 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:26:41] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:34:32,208 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:34:32] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:34:42,301 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:34:42] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:34:53,902 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:34:53] "GET /api/table-data?session_id=e62596e8-f899-42d1-9125-301caa4893bd&table=us&limit=5 HTTP/1.1" 200 -
2025-06-27 22:38:00,518 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 22:38:00,606 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 22:38:00,609 - __main__ - INFO - [m.py:410] - Connection stored for session ace5be2c-6161-495d-8c6c-6268d8d09eab: mysql/
2025-06-27 22:38:00,610 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:00] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 22:38:00,982 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:00] "GET /api/databases?session_id=ace5be2c-6161-495d-8c6c-6268d8d09eab HTTP/1.1" 200 -
2025-06-27 22:38:01,979 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:01] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 22:38:05,660 - __main__ - INFO - [m.py:486] - Updated selected database for session ace5be2c-6161-495d-8c6c-6268d8d09eab: us_data
2025-06-27 22:38:05,664 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:05] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 22:38:05,767 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:05] "GET /api/tables?session_id=ace5be2c-6161-495d-8c6c-6268d8d09eab&database=us_data HTTP/1.1" 200 -
2025-06-27 22:38:05,842 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:05] "GET /api/schema?session_id=ace5be2c-6161-495d-8c6c-6268d8d09eab&database=us_data HTTP/1.1" 200 -
2025-06-27 22:38:07,105 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 22:38:07] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-27 23:35:05,772 - __main__ - INFO - [m.py:439] - Cleaning up expired session: e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 23:35:05,801 - __main__ - INFO - [m.py:478] - Connection removed for session e62596e8-f899-42d1-9125-301caa4893bd
2025-06-27 23:40:05,907 - __main__ - INFO - [m.py:439] - Cleaning up expired session: ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 23:40:05,915 - __main__ - INFO - [m.py:478] - Connection removed for session ace5be2c-6161-495d-8c6c-6268d8d09eab
2025-06-27 23:42:36,597 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 23:42:37,964 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 23:42:39,342 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 23:42:39,344 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-27 23:49:37,724 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 9a6b6375-492c-4d35-bcf3-7b4e477aae77
2025-06-27 23:49:37,855 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:49:37,856 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 23:49:37,857 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 23:49:37,864 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:49:37,864 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 23:49:37,869 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 23:49:37,916 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 9a6b6375-492c-4d35-bcf3-7b4e477aae77
2025-06-27 23:49:37,919 - __main__ - INFO - [m.py:410] - Connection stored for session 9a6b6375-492c-4d35-bcf3-7b4e477aae77: mysql/
2025-06-27 23:49:37,923 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:37] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 23:49:38,197 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:38] "GET /api/databases?session_id=9a6b6375-492c-4d35-bcf3-7b4e477aae77 HTTP/1.1" 200 -
2025-06-27 23:49:55,139 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006
2025-06-27 23:49:55,148 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006
2025-06-27 23:49:55,149 - __main__ - INFO - [m.py:410] - Connection stored for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006: mysql/
2025-06-27 23:49:55,155 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:55] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 23:49:55,385 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:49:55] "GET /api/databases?session_id=7a3c9a76-21f3-4091-9aff-6b4b33a53006 HTTP/1.1" 200 -
2025-06-27 23:50:00,997 - __main__ - INFO - [m.py:486] - Updated selected database for session 7a3c9a76-21f3-4091-9aff-6b4b33a53006: us_data
2025-06-27 23:50:00,999 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:00] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 23:50:01,137 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:01] "GET /api/tables?session_id=7a3c9a76-21f3-4091-9aff-6b4b33a53006&database=us_data HTTP/1.1" 200 -
2025-06-27 23:50:01,243 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:01] "GET /api/schema?session_id=7a3c9a76-21f3-4091-9aff-6b4b33a53006&database=us_data HTTP/1.1" 200 -
2025-06-27 23:50:27,449 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:50:27] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-27 23:54:11,164 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-27 23:54:12,432 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-27 23:54:13,836 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-27 23:54:13,838 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-27 23:58:24,521 - __main__ - INFO - [m.py:766] - Attempting to connect to mysql database for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a
2025-06-27 23:58:24,695 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:58:24,695 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-27 23:58:24,695 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-27 23:58:24,701 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-27 23:58:24,702 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-27 23:58:24,711 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-27 23:58:24,787 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a
2025-06-27 23:58:24,789 - __main__ - INFO - [m.py:410] - Connection stored for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a: mysql/
2025-06-27 23:58:24,791 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:24] "POST /api/connect HTTP/1.1" 200 -
2025-06-27 23:58:25,033 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:25] "GET /api/databases?session_id=2b62b4ad-2ee9-404e-8cc8-448567829c5a HTTP/1.1" 200 -
2025-06-27 23:58:29,371 - __main__ - INFO - [m.py:486] - Updated selected database for session 2b62b4ad-2ee9-404e-8cc8-448567829c5a: us_data
2025-06-27 23:58:29,374 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:29] "POST /api/select-database HTTP/1.1" 200 -
2025-06-27 23:58:29,590 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:29] "GET /api/tables?session_id=2b62b4ad-2ee9-404e-8cc8-448567829c5a&database=us_data HTTP/1.1" 200 -
2025-06-27 23:58:29,829 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:29] "GET /api/schema?session_id=2b62b4ad-2ee9-404e-8cc8-448567829c5a&database=us_data HTTP/1.1" 200 -
2025-06-27 23:58:48,201 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [27/Jun/2025 23:58:48] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 00:23:38,755 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 00:23:40,210 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 00:29:17,477 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 00:29:17,478 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 00:29:17,484 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 00:29:17,936 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 00:29:17,936 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 00:33:13,522 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 032809c9-8289-4ac8-b02d-f50ec0649dd4
2025-06-28 00:33:13,656 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 00:33:13,657 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 00:33:13,657 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 00:33:13,660 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 00:33:13,661 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 00:33:13,663 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 00:33:13,695 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 032809c9-8289-4ac8-b02d-f50ec0649dd4
2025-06-28 00:33:13,697 - __main__ - INFO - [m.py:410] - Connection stored for session 032809c9-8289-4ac8-b02d-f50ec0649dd4: mysql/
2025-06-28 00:33:13,699 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:13] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 00:33:13,967 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:13] "GET /api/databases?session_id=032809c9-8289-4ac8-b02d-f50ec0649dd4 HTTP/1.1" 200 -
2025-06-28 00:33:16,631 - __main__ - INFO - [m.py:486] - Updated selected database for session 032809c9-8289-4ac8-b02d-f50ec0649dd4: us_data
2025-06-28 00:33:16,634 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:16] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 00:33:16,760 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:16] "GET /api/tables?session_id=032809c9-8289-4ac8-b02d-f50ec0649dd4&database=us_data HTTP/1.1" 200 -
2025-06-28 00:33:16,900 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:33:16] "GET /api/schema?session_id=032809c9-8289-4ac8-b02d-f50ec0649dd4&database=us_data HTTP/1.1" 200 -
2025-06-28 00:36:05,143 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session f67a8686-c4e9-4ac2-8dfb-772e068e1143
2025-06-28 00:36:05,221 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session f67a8686-c4e9-4ac2-8dfb-772e068e1143
2025-06-28 00:36:05,234 - __main__ - INFO - [m.py:410] - Connection stored for session f67a8686-c4e9-4ac2-8dfb-772e068e1143: mysql/
2025-06-28 00:36:05,239 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:36:05] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 00:36:05,439 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:36:05] "GET /api/databases?session_id=f67a8686-c4e9-4ac2-8dfb-772e068e1143 HTTP/1.1" 200 -
2025-06-28 00:36:08,156 - __main__ - INFO - [m.py:486] - Updated selected database for session f67a8686-c4e9-4ac2-8dfb-772e068e1143: us_data
2025-06-28 00:36:08,158 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:36:08] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 00:36:08,223 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:36:08] "GET /api/tables?session_id=f67a8686-c4e9-4ac2-8dfb-772e068e1143&database=us_data HTTP/1.1" 200 -
2025-06-28 00:36:08,355 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:36:08] "GET /api/schema?session_id=f67a8686-c4e9-4ac2-8dfb-772e068e1143&database=us_data HTTP/1.1" 200 -
2025-06-28 00:38:41,222 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 1959b70f-46e1-44b1-ab76-db5526bb44f3
2025-06-28 00:38:41,408 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 1959b70f-46e1-44b1-ab76-db5526bb44f3
2025-06-28 00:38:41,412 - __main__ - INFO - [m.py:410] - Connection stored for session 1959b70f-46e1-44b1-ab76-db5526bb44f3: mysql/
2025-06-28 00:38:41,463 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:38:41] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 00:38:41,596 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:38:41] "GET /api/databases?session_id=1959b70f-46e1-44b1-ab76-db5526bb44f3 HTTP/1.1" 200 -
2025-06-28 00:38:44,920 - __main__ - INFO - [m.py:486] - Updated selected database for session 1959b70f-46e1-44b1-ab76-db5526bb44f3: us_data
2025-06-28 00:38:44,922 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:38:44] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 00:38:45,085 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:38:45] "GET /api/tables?session_id=1959b70f-46e1-44b1-ab76-db5526bb44f3&database=us_data HTTP/1.1" 200 -
2025-06-28 00:38:45,218 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:38:45] "GET /api/schema?session_id=1959b70f-46e1-44b1-ab76-db5526bb44f3&database=us_data HTTP/1.1" 200 -
2025-06-28 00:39:14,244 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:39:14] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 00:41:14,374 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:41:14] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 00:42:03,659 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:42:03] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 00:42:23,686 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:42:23] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 00:42:38,836 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 00:42:38] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 01:10:00,646 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 01:10:03,202 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:10:05,209 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:10:05,210 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:10:25,893 - __main__ - ERROR - [m.py:788] - Failed to get schema info: No active connection found
2025-06-28 01:10:25,893 - __main__ - ERROR - [m.py:1124] - AI query error: No active connection found
2025-06-28 01:10:25,896 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:10:25] "[35m[1mPOST /api/ai-query HTTP/1.1[0m" 500 -
2025-06-28 01:12:45,673 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 01:12:47,178 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:12:48,970 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:12:48,971 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:13:00,737 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:00] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 01:13:17,535 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session e11b5bc4-c216-4083-8151-4c70c95a02d9
2025-06-28 01:13:17,789 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:13:17,790 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 01:13:17,790 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 01:13:17,811 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:13:17,815 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 01:13:17,823 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 01:13:17,874 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e11b5bc4-c216-4083-8151-4c70c95a02d9
2025-06-28 01:13:17,875 - __main__ - INFO - [m.py:410] - Connection stored for session e11b5bc4-c216-4083-8151-4c70c95a02d9: mysql/
2025-06-28 01:13:17,879 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:17] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:13:18,238 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:18] "GET /api/databases?session_id=e11b5bc4-c216-4083-8151-4c70c95a02d9 HTTP/1.1" 200 -
2025-06-28 01:13:19,689 - __main__ - INFO - [m.py:486] - Updated selected database for session e11b5bc4-c216-4083-8151-4c70c95a02d9: us_data
2025-06-28 01:13:19,690 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:19] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 01:13:19,759 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:19] "GET /api/tables?session_id=e11b5bc4-c216-4083-8151-4c70c95a02d9&database=us_data HTTP/1.1" 200 -
2025-06-28 01:13:20,059 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:20] "GET /api/schema?session_id=e11b5bc4-c216-4083-8151-4c70c95a02d9&database=us_data HTTP/1.1" 200 -
2025-06-28 01:13:56,856 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 01:13:56,874 - __main__ - ERROR - [m.py:881] - Query execution failed: Query validation failed: Only SELECT queries are allowed
2025-06-28 01:13:56,875 - __main__ - ERROR - [m.py:1127] - Query execution failed: Query validation failed: Only SELECT queries are allowed
2025-06-28 01:13:56,878 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:13:56] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 01:21:01,473 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 01:21:03,119 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:21:04,468 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:21:04,469 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:21:11,113 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:21:11] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 01:23:23,071 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 01:23:23,518 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:23:25,070 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:23:25,072 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:23:38,557 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:23:38] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 01:26:44,703 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 01:26:46,251 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:26:47,139 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:26:47,140 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:27:23,416 - __main__ - INFO - [m.py:1037] - AI query request received: session_id=e11b5bc4-c216-4083-8151-4c70c95a02d9, database=us_data, question='Give top 5 email from us'
2025-06-28 01:27:23,417 - __main__ - ERROR - [m.py:1050] - No active connection found for session e11b5bc4-c216-4083-8151-4c70c95a02d9
2025-06-28 01:27:23,419 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:27:23] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 01:34:21,852 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 01:34:23,344 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:34:24,880 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:34:24,881 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:34:39,986 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=e11b5bc4-c216-4083-8151-4c70c95a02d9, database=us_data, question='Give top 5 email from us'
2025-06-28 01:34:39,987 - __main__ - ERROR - [m.py:1052] - No active connection found for session e11b5bc4-c216-4083-8151-4c70c95a02d9
2025-06-28 01:34:39,988 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:34:39] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 01:34:59,329 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 681dbc2b-ef62-4638-90a1-e8691ed1a4c9
2025-06-28 01:34:59,484 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:34:59,484 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 01:34:59,485 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 01:34:59,493 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:34:59,493 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 01:34:59,496 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 01:34:59,554 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 681dbc2b-ef62-4638-90a1-e8691ed1a4c9
2025-06-28 01:34:59,555 - __main__ - INFO - [m.py:410] - Connection stored for session 681dbc2b-ef62-4638-90a1-e8691ed1a4c9: mysql/
2025-06-28 01:34:59,559 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:34:59] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:34:59,887 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:34:59] "GET /api/databases?session_id=681dbc2b-ef62-4638-90a1-e8691ed1a4c9 HTTP/1.1" 200 -
2025-06-28 01:35:02,082 - __main__ - INFO - [m.py:486] - Updated selected database for session 681dbc2b-ef62-4638-90a1-e8691ed1a4c9: us_data
2025-06-28 01:35:02,083 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:35:02] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 01:35:02,232 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:35:02] "GET /api/tables?session_id=681dbc2b-ef62-4638-90a1-e8691ed1a4c9&database=us_data HTTP/1.1" 200 -
2025-06-28 01:35:02,398 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:35:02] "GET /api/schema?session_id=681dbc2b-ef62-4638-90a1-e8691ed1a4c9&database=us_data HTTP/1.1" 200 -
2025-06-28 01:35:05,081 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=681dbc2b-ef62-4638-90a1-e8691ed1a4c9, database=us_data, question='Give top 5 email from us'
2025-06-28 01:35:05,221 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 01:35:05,349 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 01:35:06,873 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 01:35:06,895 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 01:35:06,901 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-28 01:35:06,915 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 01:35:06,919 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:35:06] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 01:36:05,010 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 01:36:05,011 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 01:36:05,014 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:36:05,549 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:36:05,550 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:36:23,243 - __main__ - INFO - [m.py:902] - Attempting to connect to mongodb database for session 6fd8d307-745a-4c8d-bc12-5ef726dedd7b
2025-06-28 01:36:23,244 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-28 01:36:23,245 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-27T20:06:23.244997+00:00', 'session_id': '6fd8d307-745a-4c8d-bc12-5ef726dedd7b', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-28 01:36:23,246 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:36:23] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-28 01:36:37,408 - __main__ - INFO - [m.py:902] - Attempting to connect to mongodb database for session 3a540c5c-66ad-4a71-bcd7-93494aff1a6f
2025-06-28 01:36:37,410 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-28 01:36:37,411 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-27T20:06:37.411254+00:00', 'session_id': '3a540c5c-66ad-4a71-bcd7-93494aff1a6f', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-28 01:36:37,414 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:36:37] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-28 01:38:28,928 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session a2d62326-cc03-484e-8c5c-cf2e9012aad5
2025-06-28 01:38:29,014 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:38:29,015 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 01:38:29,015 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 01:38:29,020 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:38:29,021 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 01:38:29,024 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 01:38:29,080 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session a2d62326-cc03-484e-8c5c-cf2e9012aad5
2025-06-28 01:38:29,082 - __main__ - INFO - [m.py:410] - Connection stored for session a2d62326-cc03-484e-8c5c-cf2e9012aad5: mysql/
2025-06-28 01:38:29,085 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:38:29] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:38:29,396 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:38:29] "GET /api/databases?session_id=a2d62326-cc03-484e-8c5c-cf2e9012aad5 HTTP/1.1" 200 -
2025-06-28 01:38:31,912 - __main__ - INFO - [m.py:486] - Updated selected database for session a2d62326-cc03-484e-8c5c-cf2e9012aad5: us_data
2025-06-28 01:38:31,914 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:38:31] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 01:38:32,004 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:38:32] "GET /api/tables?session_id=a2d62326-cc03-484e-8c5c-cf2e9012aad5&database=us_data HTTP/1.1" 200 -
2025-06-28 01:38:32,103 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:38:32] "GET /api/schema?session_id=a2d62326-cc03-484e-8c5c-cf2e9012aad5&database=us_data HTTP/1.1" 200 -
2025-06-28 01:39:07,269 - __main__ - INFO - [m.py:902] - Attempting to connect to mongodb database for session 1522ed3a-f8a1-469e-b7a4-eaa907f112b6
2025-06-28 01:39:07,351 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session 1522ed3a-f8a1-469e-b7a4-eaa907f112b6
2025-06-28 01:39:07,352 - __main__ - INFO - [m.py:410] - Connection stored for session 1522ed3a-f8a1-469e-b7a4-eaa907f112b6: mongodb/
2025-06-28 01:39:07,352 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:39:07] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:39:07,373 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-28 01:39:07,379 - __main__ - ERROR - [m.py:570] - Failed to get databases: Unsupported database type: mongodb
2025-06-28 01:39:07,380 - __main__ - ERROR - [m.py:946] - Error fetching databases: Unsupported database type: mongodb
2025-06-28 01:39:07,383 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:39:07] "[35m[1mGET /api/databases?session_id=1522ed3a-f8a1-469e-b7a4-eaa907f112b6 HTTP/1.1[0m" 500 -
2025-06-28 01:39:34,166 - __main__ - INFO - [m.py:902] - Attempting to connect to mongodb database for session a1b92e5d-3cbc-4f21-ab69-a075f515d812
2025-06-28 01:39:34,180 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session a1b92e5d-3cbc-4f21-ab69-a075f515d812
2025-06-28 01:39:34,180 - __main__ - INFO - [m.py:410] - Connection stored for session a1b92e5d-3cbc-4f21-ab69-a075f515d812: mongodb/
2025-06-28 01:39:34,182 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:39:34] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:39:34,210 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-28 01:39:34,213 - __main__ - ERROR - [m.py:570] - Failed to get databases: Unsupported database type: mongodb
2025-06-28 01:39:34,213 - __main__ - ERROR - [m.py:946] - Error fetching databases: Unsupported database type: mongodb
2025-06-28 01:39:34,217 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:39:34] "[35m[1mGET /api/databases?session_id=a1b92e5d-3cbc-4f21-ab69-a075f515d812 HTTP/1.1[0m" 500 -
2025-06-28 01:40:48,790 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 6dc55143-c37a-45d7-88a1-957539d2a5c2
2025-06-28 01:40:48,823 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 6dc55143-c37a-45d7-88a1-957539d2a5c2
2025-06-28 01:40:48,824 - __main__ - INFO - [m.py:410] - Connection stored for session 6dc55143-c37a-45d7-88a1-957539d2a5c2: mysql/
2025-06-28 01:40:48,825 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:40:48] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:40:48,877 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:40:48] "GET /api/databases?session_id=6dc55143-c37a-45d7-88a1-957539d2a5c2 HTTP/1.1" 200 -
2025-06-28 01:40:51,694 - __main__ - INFO - [m.py:486] - Updated selected database for session 6dc55143-c37a-45d7-88a1-957539d2a5c2: us_data
2025-06-28 01:40:51,696 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:40:51] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 01:40:51,737 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:40:51] "GET /api/tables?session_id=6dc55143-c37a-45d7-88a1-957539d2a5c2&database=us_data HTTP/1.1" 200 -
2025-06-28 01:40:51,866 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:40:51] "GET /api/schema?session_id=6dc55143-c37a-45d7-88a1-957539d2a5c2&database=us_data HTTP/1.1" 200 -
2025-06-28 01:41:21,301 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=6dc55143-c37a-45d7-88a1-957539d2a5c2, database=us_data, question='Give top 5 email from us'
2025-06-28 01:41:21,374 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 01:41:21,636 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 01:41:23,481 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 01:41:23,565 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 01:41:23,566 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-28 01:41:23,577 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 01:41:23,579 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:41:23] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 01:57:11,174 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=6dc55143-c37a-45d7-88a1-957539d2a5c2, database=us_data, question='Give top 5 email from us'
2025-06-28 01:57:11,613 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 01:57:11,684 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 01:57:13,738 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 01:57:13,773 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 01:57:13,781 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT DISTINCT email FROM us LIMIT 5;...
2025-06-28 01:57:13,808 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 01:57:13,823 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:13] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 01:57:46,527 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 28875a21-1763-45fd-810e-fda3a44c0e56
2025-06-28 01:57:46,539 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 28875a21-1763-45fd-810e-fda3a44c0e56
2025-06-28 01:57:46,540 - __main__ - INFO - [m.py:410] - Connection stored for session 28875a21-1763-45fd-810e-fda3a44c0e56: mysql/
2025-06-28 01:57:46,542 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:46] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:57:46,812 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:46] "GET /api/databases?session_id=28875a21-1763-45fd-810e-fda3a44c0e56 HTTP/1.1" 200 -
2025-06-28 01:57:48,784 - __main__ - INFO - [m.py:486] - Updated selected database for session 28875a21-1763-45fd-810e-fda3a44c0e56: us_data
2025-06-28 01:57:48,785 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:48] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 01:57:48,901 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:48] "GET /api/tables?session_id=28875a21-1763-45fd-810e-fda3a44c0e56&database=us_data HTTP/1.1" 200 -
2025-06-28 01:57:49,001 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:49] "GET /api/schema?session_id=28875a21-1763-45fd-810e-fda3a44c0e56&database=us_data HTTP/1.1" 200 -
2025-06-28 01:57:52,826 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=28875a21-1763-45fd-810e-fda3a44c0e56, database=us_data, question='Give top 5 email from us'
2025-06-28 01:57:52,923 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 01:57:52,943 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 01:57:54,478 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 01:57:54,482 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 01:57:54,483 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-28 01:57:54,486 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 01:57:54,488 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:57:54] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 01:59:10,318 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 01:59:10,327 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 01:59:10,334 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 01:59:10,918 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 01:59:10,919 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 01:59:40,316 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5
2025-06-28 01:59:41,249 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:59:41,250 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 01:59:41,250 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 01:59:41,255 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 01:59:41,256 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 01:59:41,261 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 01:59:41,345 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5
2025-06-28 01:59:41,346 - __main__ - INFO - [m.py:410] - Connection stored for session 32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5: mysql/
2025-06-28 01:59:41,349 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:59:41] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 01:59:41,569 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:59:41] "GET /api/databases?session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5 HTTP/1.1" 200 -
2025-06-28 01:59:46,133 - __main__ - INFO - [m.py:486] - Updated selected database for session 32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5: us_data
2025-06-28 01:59:46,134 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:59:46] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 01:59:46,200 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:59:46] "GET /api/tables?session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5&database=us_data HTTP/1.1" 200 -
2025-06-28 01:59:46,298 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 01:59:46] "GET /api/schema?session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5&database=us_data HTTP/1.1" 200 -
2025-06-28 02:00:09,759 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5, database=us_data, question='Give top 5 email from us'
2025-06-28 02:00:09,847 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 02:00:10,092 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 02:00:12,515 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 02:00:12,522 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 02:00:12,523 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us ORDER BY email LIMIT 5;...
2025-06-28 02:00:12,618 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 02:00:12,623 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 02:00:12] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 02:03:09,345 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5, database=us_data, question='Give top 5 email from us'
2025-06-28 02:03:09,660 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 02:03:09,682 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 02:03:11,309 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 02:03:11,324 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 02:03:11,325 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-28 02:03:11,330 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 02:03:11,331 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 02:03:11] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 02:03:58,421 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 02:03:58,781 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 02:04:01,267 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 02:04:01,268 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 02:04:08,007 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5, database=us_data, question='Give top 5 email from us'
2025-06-28 02:04:08,007 - __main__ - ERROR - [m.py:1052] - No active connection found for session 32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5
2025-06-28 02:04:08,008 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 02:04:08] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 02:04:22,898 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 02:04:23,169 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 02:04:24,956 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 02:04:24,957 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 792-167-438
2025-06-28 02:04:26,255 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5, database=us_data, question='Give top 5 email from us'
2025-06-28 02:04:26,256 - __main__ - ERROR - [m.py:1052] - No active connection found for session 32fdc1ec-82cc-47d0-a7cd-ae2c9e9dd4c5
2025-06-28 02:04:26,259 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 02:04:26] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 13:49:10,649 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 13:49:10,650 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 13:49:10,653 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 13:49:11,177 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 13:49:11,281 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 118-423-803
2025-06-28 13:49:34,772 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 46885a91-26eb-4f61-9b3b-c65938f70f45
2025-06-28 13:49:34,940 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 13:49:34,940 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 13:49:34,940 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 13:49:34,948 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 13:49:34,948 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 13:49:34,950 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 13:49:34,983 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 46885a91-26eb-4f61-9b3b-c65938f70f45
2025-06-28 13:49:34,983 - __main__ - INFO - [m.py:410] - Connection stored for session 46885a91-26eb-4f61-9b3b-c65938f70f45: mysql/
2025-06-28 13:49:34,984 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 13:49:34] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 13:49:35,264 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 13:49:35] "GET /api/databases?session_id=46885a91-26eb-4f61-9b3b-c65938f70f45 HTTP/1.1" 200 -
2025-06-28 13:49:37,471 - __main__ - INFO - [m.py:486] - Updated selected database for session 46885a91-26eb-4f61-9b3b-c65938f70f45: us_data
2025-06-28 13:49:37,472 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 13:49:37] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 13:49:37,554 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 13:49:37] "GET /api/tables?session_id=46885a91-26eb-4f61-9b3b-c65938f70f45&database=us_data HTTP/1.1" 200 -
2025-06-28 13:49:37,722 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 13:49:37] "GET /api/schema?session_id=46885a91-26eb-4f61-9b3b-c65938f70f45&database=us_data HTTP/1.1" 200 -
2025-06-28 13:49:55,523 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=46885a91-26eb-4f61-9b3b-c65938f70f45, database=us_data, question='Give top 5 email from us'
2025-06-28 13:49:55,600 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 13:49:55,802 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 13:50:15,994 - groq._base_client - INFO - [_base_client.py:1058] - Retrying request to /openai/v1/chat/completions in 0.433750 seconds
2025-06-28 13:50:18,208 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 13:50:18,218 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 13:50:18,222 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us ORDER BY email ASC LIMIT 5...
2025-06-28 13:50:18,256 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 13:50:18,258 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 13:50:18] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 14:02:48,658 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 14:02:49,676 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 14:02:50,678 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 14:02:50,679 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 118-423-803
2025-06-28 14:02:52,766 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 14:02:52,946 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 14:02:53,695 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 14:02:53,695 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 118-423-803
2025-06-28 14:03:11,463 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=46885a91-26eb-4f61-9b3b-c65938f70f45, database=us_data, question='Give top 5 email from us'
2025-06-28 14:03:11,464 - __main__ - ERROR - [m.py:1052] - No active connection found for session 46885a91-26eb-4f61-9b3b-c65938f70f45
2025-06-28 14:03:11,468 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 14:03:11] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 15:16:47,635 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=46885a91-26eb-4f61-9b3b-c65938f70f45, database=us_data, question='Give top 5 email from us'
2025-06-28 15:16:47,636 - __main__ - ERROR - [m.py:1052] - No active connection found for session 46885a91-26eb-4f61-9b3b-c65938f70f45
2025-06-28 15:16:47,638 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 15:16:47] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 23:12:14,571 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 23:12:14,572 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 23:12:14,576 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 23:12:15,523 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 23:12:15,685 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 113-779-447
2025-06-28 23:12:59,478 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session f18375b9-bcc9-49bc-ab4d-49c51556e31a
2025-06-28 23:12:59,603 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 23:12:59,603 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 23:12:59,604 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 23:12:59,616 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 23:12:59,618 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 23:12:59,619 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 23:12:59,660 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session f18375b9-bcc9-49bc-ab4d-49c51556e31a
2025-06-28 23:12:59,662 - __main__ - INFO - [m.py:410] - Connection stored for session f18375b9-bcc9-49bc-ab4d-49c51556e31a: mysql/
2025-06-28 23:12:59,672 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:12:59] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 23:13:00,026 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:13:00] "GET /api/databases?session_id=f18375b9-bcc9-49bc-ab4d-49c51556e31a HTTP/1.1" 200 -
2025-06-28 23:13:03,172 - __main__ - INFO - [m.py:486] - Updated selected database for session f18375b9-bcc9-49bc-ab4d-49c51556e31a: us_data
2025-06-28 23:13:03,174 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:13:03] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 23:13:03,261 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:13:03] "GET /api/tables?session_id=f18375b9-bcc9-49bc-ab4d-49c51556e31a&database=us_data HTTP/1.1" 200 -
2025-06-28 23:13:03,364 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:13:03] "GET /api/schema?session_id=f18375b9-bcc9-49bc-ab4d-49c51556e31a&database=us_data HTTP/1.1" 200 -
2025-06-28 23:13:06,331 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=f18375b9-bcc9-49bc-ab4d-49c51556e31a, database=us_data, question='Give top 5 email from us'
2025-06-28 23:13:06,393 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 23:13:06,593 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 23:13:08,523 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 23:13:08,555 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 23:13:08,557 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-28 23:13:08,669 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 23:13:08,672 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:13:08] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-28 23:47:47,460 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 23:47:49,180 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 23:47:51,337 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 23:47:51,338 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 113-779-447
2025-06-28 23:49:09,657 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-28 23:49:09,850 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 23:51:12,544 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-28 23:51:12,545 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-28 23:51:12,547 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-28 23:51:13,603 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-28 23:51:13,609 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 113-779-447
2025-06-28 23:51:17,618 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=f18375b9-bcc9-49bc-ab4d-49c51556e31a, database=us_data, question='Give top 5 email from us'
2025-06-28 23:51:17,622 - __main__ - ERROR - [m.py:1052] - No active connection found for session f18375b9-bcc9-49bc-ab4d-49c51556e31a
2025-06-28 23:51:17,629 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:17] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 23:51:23,792 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=f18375b9-bcc9-49bc-ab4d-49c51556e31a, database=us_data, question='Give top 5 email from us'
2025-06-28 23:51:23,793 - __main__ - ERROR - [m.py:1052] - No active connection found for session f18375b9-bcc9-49bc-ab4d-49c51556e31a
2025-06-28 23:51:23,795 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:23] "[31m[1mPOST /api/ai-query HTTP/1.1[0m" 400 -
2025-06-28 23:51:32,257 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 5c1927c2-e163-40f9-a1ec-da0f72ef4d04
2025-06-28 23:51:32,381 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 23:51:32,382 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-28 23:51:32,382 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-28 23:51:32,389 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-28 23:51:32,389 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-28 23:51:32,395 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-28 23:51:32,444 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5c1927c2-e163-40f9-a1ec-da0f72ef4d04
2025-06-28 23:51:32,446 - __main__ - INFO - [m.py:410] - Connection stored for session 5c1927c2-e163-40f9-a1ec-da0f72ef4d04: mysql/
2025-06-28 23:51:32,451 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:32] "POST /api/connect HTTP/1.1" 200 -
2025-06-28 23:51:32,736 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:32] "GET /api/databases?session_id=5c1927c2-e163-40f9-a1ec-da0f72ef4d04 HTTP/1.1" 200 -
2025-06-28 23:51:34,395 - __main__ - INFO - [m.py:486] - Updated selected database for session 5c1927c2-e163-40f9-a1ec-da0f72ef4d04: us_data
2025-06-28 23:51:34,397 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:34] "POST /api/select-database HTTP/1.1" 200 -
2025-06-28 23:51:34,513 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:34] "GET /api/tables?session_id=5c1927c2-e163-40f9-a1ec-da0f72ef4d04&database=us_data HTTP/1.1" 200 -
2025-06-28 23:51:34,645 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:51:34] "GET /api/schema?session_id=5c1927c2-e163-40f9-a1ec-da0f72ef4d04&database=us_data HTTP/1.1" 200 -
2025-06-28 23:52:11,727 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=5c1927c2-e163-40f9-a1ec-da0f72ef4d04, database=us_data, question='Give top 5 email from us'
2025-06-28 23:52:11,804 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-28 23:52:12,024 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-28 23:52:13,747 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-28 23:52:13,758 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-28 23:52:13,759 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-28 23:52:13,766 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-28 23:52:13,768 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [28/Jun/2025 23:52:13] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 00:01:38,483 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=5c1927c2-e163-40f9-a1ec-da0f72ef4d04, database=us_data, question='Give top 5 email from us'
2025-06-29 00:01:39,195 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-29 00:01:39,248 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-29 00:01:40,850 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 00:01:40,877 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 00:01:40,878 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-29 00:01:40,889 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-29 00:01:40,895 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:01:40] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 00:02:03,614 - __main__ - INFO - [m.py:902] - Attempting to connect to mysql database for session 464976ad-63c1-4e35-a044-be99899c1e23
2025-06-29 00:02:03,619 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 464976ad-63c1-4e35-a044-be99899c1e23
2025-06-29 00:02:03,620 - __main__ - INFO - [m.py:410] - Connection stored for session 464976ad-63c1-4e35-a044-be99899c1e23: mysql/
2025-06-29 00:02:03,622 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:02:03] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 00:02:03,761 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:02:03] "GET /api/databases?session_id=464976ad-63c1-4e35-a044-be99899c1e23 HTTP/1.1" 200 -
2025-06-29 00:02:06,970 - __main__ - INFO - [m.py:486] - Updated selected database for session 464976ad-63c1-4e35-a044-be99899c1e23: us_data
2025-06-29 00:02:06,971 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:02:06] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 00:02:07,043 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:02:07] "GET /api/tables?session_id=464976ad-63c1-4e35-a044-be99899c1e23&database=us_data HTTP/1.1" 200 -
2025-06-29 00:02:07,135 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:02:07] "GET /api/schema?session_id=464976ad-63c1-4e35-a044-be99899c1e23&database=us_data HTTP/1.1" 200 -
2025-06-29 00:02:29,281 - __main__ - INFO - [m.py:1038] - AI query request received: session_id=464976ad-63c1-4e35-a044-be99899c1e23, database=us_data, question='Give top 5 email from us'
2025-06-29 00:02:29,396 - __main__ - INFO - [m.py:1112] - Retrieved schema for database us_data
2025-06-29 00:02:29,430 - __main__ - INFO - [m.py:1141] - Sending prompt to Groq API
2025-06-29 00:02:31,170 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 00:02:31,174 - __main__ - INFO - [m.py:1153] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 00:02:31,175 - __main__ - INFO - [m.py:1174] - Executing generated query: 

SELECT email FROM us ORDER BY email ASC LIMIT 5;...
2025-06-29 00:02:31,186 - __main__ - INFO - [m.py:1185] - Query executed successfully, returned 5 results
2025-06-29 00:02:31,190 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:02:31] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 00:12:11,703 - __main__ - INFO - [m.py:902] - Attempting to connect to mongodb database for session 0f28b5c6-4993-4f8d-84f7-c20cb3f022bb
2025-06-29 00:12:11,729 - __main__ - ERROR - [m.py:285] - Connection error: Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus
2025-06-29 00:12:11,736 - __main__ - ERROR - [m.py:533] - Connection failed: {'error_type': 'ConnectionError', 'message': 'Username and password must be escaped according to RFC 3986, use urllib.parse.quote_plus', 'timestamp': '2025-06-28T18:42:11.735506+00:00', 'session_id': '0f28b5c6-4993-4f8d-84f7-c20cb3f022bb', 'query': None, 'database': None, 'table': None, 'stack_trace': None}
2025-06-29 00:12:11,746 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:12:11] "[31m[1mPOST /api/connect HTTP/1.1[0m" 400 -
2025-06-29 00:17:15,819 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-29 00:17:17,438 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 00:17:19,694 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-29 00:17:19,695 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 113-779-447
2025-06-29 00:17:23,893 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-29 00:17:24,067 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 00:17:25,612 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-29 00:17:25,613 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 113-779-447
2025-06-29 00:17:46,708 - __main__ - INFO - [m.py:902] - Attempting to connect to mongodb database for session debe6bcf-65d0-4fb2-bb34-09d2f2a49d79
2025-06-29 00:17:46,781 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session debe6bcf-65d0-4fb2-bb34-09d2f2a49d79
2025-06-29 00:17:46,782 - __main__ - INFO - [m.py:410] - Connection stored for session debe6bcf-65d0-4fb2-bb34-09d2f2a49d79: mongodb/
2025-06-29 00:17:46,783 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:17:46] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 00:17:46,903 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 00:17:46,914 - __main__ - ERROR - [m.py:570] - Failed to get databases: Unsupported database type: mongodb
2025-06-29 00:17:46,916 - __main__ - ERROR - [m.py:946] - Error fetching databases: Unsupported database type: mongodb
2025-06-29 00:17:46,920 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:17:46] "[35m[1mGET /api/databases?session_id=debe6bcf-65d0-4fb2-bb34-09d2f2a49d79 HTTP/1.1[0m" 500 -
2025-06-29 00:28:43,213 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/m.py', reloading
2025-06-29 00:28:44,692 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 00:28:49,709 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 00:28:52,343 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 00:42:53,778 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-29 00:42:53,780 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-29 00:42:53,785 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 00:42:54,983 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-29 00:42:54,985 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 113-779-447
2025-06-29 00:43:08,655 - __main__ - INFO - [m.py:909] - Attempting to connect to mysql database for session 5ca62b4f-8bf3-434a-a18f-b10a0d26caac
2025-06-29 00:43:08,801 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-29 00:43:08,801 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-29 00:43:08,802 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-29 00:43:08,806 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-29 00:43:08,807 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-29 00:43:08,810 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-29 00:43:08,864 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 5ca62b4f-8bf3-434a-a18f-b10a0d26caac
2025-06-29 00:43:08,864 - __main__ - INFO - [m.py:410] - Connection stored for session 5ca62b4f-8bf3-434a-a18f-b10a0d26caac: mysql/
2025-06-29 00:43:08,866 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:43:08] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 00:43:09,185 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:43:09] "GET /api/databases?session_id=5ca62b4f-8bf3-434a-a18f-b10a0d26caac HTTP/1.1" 200 -
2025-06-29 00:43:11,779 - __main__ - INFO - [m.py:486] - Updated selected database for session 5ca62b4f-8bf3-434a-a18f-b10a0d26caac: us_data
2025-06-29 00:43:11,781 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:43:11] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 00:43:11,941 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:43:11] "GET /api/tables?session_id=5ca62b4f-8bf3-434a-a18f-b10a0d26caac&database=us_data HTTP/1.1" 200 -
2025-06-29 00:43:12,070 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:43:12] "GET /api/schema?session_id=5ca62b4f-8bf3-434a-a18f-b10a0d26caac&database=us_data HTTP/1.1" 200 -
2025-06-29 00:43:30,546 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=5ca62b4f-8bf3-434a-a18f-b10a0d26caac, database=us_data, question='Give top 5 email from us'
2025-06-29 00:43:30,620 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 00:43:30,795 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 00:43:32,655 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 00:43:32,683 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 00:43:32,687 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-29 00:43:32,702 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 5 results
2025-06-29 00:43:32,706 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 00:43:32] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 01:37:44,925 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=5ca62b4f-8bf3-434a-a18f-b10a0d26caac, database=us_data, question='Give top 5 email from us'
2025-06-29 01:37:45,424 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 01:37:45,493 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 01:37:47,370 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 01:37:47,410 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 01:37:47,413 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT email FROM us LIMIT 5;...
2025-06-29 01:37:47,445 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 5 results
2025-06-29 01:37:47,479 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:37:47] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 01:38:03,652 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=5ca62b4f-8bf3-434a-a18f-b10a0d26caac, database=us_data, question='Give top 6 email from us'
2025-06-29 01:38:03,745 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 01:38:03,778 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 01:38:05,267 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 01:38:05,270 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 01:38:05,271 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT email FROM us LIMIT 6;...
2025-06-29 01:38:05,278 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 6 results
2025-06-29 01:38:05,280 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:38:05] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 01:48:13,484 - __main__ - INFO - [m.py:909] - Attempting to connect to mysql database for session 36dcd0f8-5eea-443a-97a5-187cf15cccef
2025-06-29 01:48:13,605 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 36dcd0f8-5eea-443a-97a5-187cf15cccef
2025-06-29 01:48:13,606 - __main__ - INFO - [m.py:410] - Connection stored for session 36dcd0f8-5eea-443a-97a5-187cf15cccef: mysql/
2025-06-29 01:48:13,629 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:48:13] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 01:48:13,830 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:48:13] "GET /api/databases?session_id=36dcd0f8-5eea-443a-97a5-187cf15cccef HTTP/1.1" 200 -
2025-06-29 01:48:19,231 - __main__ - INFO - [m.py:486] - Updated selected database for session 36dcd0f8-5eea-443a-97a5-187cf15cccef: us_data
2025-06-29 01:48:19,233 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:48:19] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 01:48:19,341 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:48:19] "GET /api/tables?session_id=36dcd0f8-5eea-443a-97a5-187cf15cccef&database=us_data HTTP/1.1" 200 -
2025-06-29 01:48:19,509 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:48:19] "GET /api/schema?session_id=36dcd0f8-5eea-443a-97a5-187cf15cccef&database=us_data HTTP/1.1" 200 -
2025-06-29 01:48:57,146 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=36dcd0f8-5eea-443a-97a5-187cf15cccef, database=us_data, question='top 3 email from us'
2025-06-29 01:48:57,239 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 01:48:57,322 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 01:48:59,193 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 01:48:59,223 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 01:48:59,224 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT email FROM us GROUP BY email ORDER BY COU...
2025-06-29 01:48:59,245 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 3 results
2025-06-29 01:48:59,248 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:48:59] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 01:50:10,870 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=36dcd0f8-5eea-443a-97a5-187cf15cccef, database=us_data, question='top 13 email from us'
2025-06-29 01:50:11,048 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 01:50:11,090 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 01:50:12,728 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 01:50:12,736 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 01:50:12,737 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT email FROM us LIMIT 13;...
2025-06-29 01:50:12,743 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 13 results
2025-06-29 01:50:12,746 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:50:12] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 01:57:12,087 - __main__ - INFO - [m.py:909] - Attempting to connect to mysql database for session 2acf2a3b-69a4-423b-b2f1-5cac11b03651
2025-06-29 01:57:12,229 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session 2acf2a3b-69a4-423b-b2f1-5cac11b03651
2025-06-29 01:57:12,231 - __main__ - INFO - [m.py:410] - Connection stored for session 2acf2a3b-69a4-423b-b2f1-5cac11b03651: mysql/
2025-06-29 01:57:12,247 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:57:12] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 01:57:12,474 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:57:12] "GET /api/databases?session_id=2acf2a3b-69a4-423b-b2f1-5cac11b03651 HTTP/1.1" 200 -
2025-06-29 01:57:15,120 - __main__ - INFO - [m.py:486] - Updated selected database for session 2acf2a3b-69a4-423b-b2f1-5cac11b03651: us_data
2025-06-29 01:57:15,122 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:57:15] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 01:57:15,245 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:57:15] "GET /api/tables?session_id=2acf2a3b-69a4-423b-b2f1-5cac11b03651&database=us_data HTTP/1.1" 200 -
2025-06-29 01:57:15,610 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:57:15] "GET /api/schema?session_id=2acf2a3b-69a4-423b-b2f1-5cac11b03651&database=us_data HTTP/1.1" 200 -
2025-06-29 01:57:52,032 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=2acf2a3b-69a4-423b-b2f1-5cac11b03651, database=us_data, question='top 3 name from us'
2025-06-29 01:57:52,116 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 01:57:52,160 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 01:57:53,377 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 01:57:53,428 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 01:57:53,430 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT CONCAT(first_name, ' ', last_name) AS nam...
2025-06-29 01:57:53,472 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 3 results
2025-06-29 01:57:53,479 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 01:57:53] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 02:22:10,109 - __main__ - INFO - [m.py:909] - Attempting to connect to mongodb database for session d03fd32a-6f9a-4f99-8d4d-264701417904
2025-06-29 02:22:10,562 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session d03fd32a-6f9a-4f99-8d4d-264701417904
2025-06-29 02:22:10,564 - __main__ - INFO - [m.py:410] - Connection stored for session d03fd32a-6f9a-4f99-8d4d-264701417904: mongodb/
2025-06-29 02:22:10,623 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:10] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 02:22:10,942 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:10] "GET /api/databases?session_id=d03fd32a-6f9a-4f99-8d4d-264701417904 HTTP/1.1" 200 -
2025-06-29 02:22:15,519 - __main__ - INFO - [m.py:486] - Updated selected database for session d03fd32a-6f9a-4f99-8d4d-264701417904: stress_assessment
2025-06-29 02:22:15,523 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:15] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 02:22:15,607 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 02:22:15,612 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 02:22:15,612 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 02:22:15,616 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:15] "[35m[1mGET /api/tables?session_id=d03fd32a-6f9a-4f99-8d4d-264701417904&database=stress_assessment HTTP/1.1[0m" 500 -
2025-06-29 02:22:23,669 - __main__ - INFO - [m.py:486] - Updated selected database for session d03fd32a-6f9a-4f99-8d4d-264701417904: chat_app
2025-06-29 02:22:23,673 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:23] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 02:22:23,724 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 02:22:23,727 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 02:22:23,730 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 02:22:23,731 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:23] "[35m[1mGET /api/tables?session_id=d03fd32a-6f9a-4f99-8d4d-264701417904&database=chat_app HTTP/1.1[0m" 500 -
2025-06-29 02:22:36,248 - __main__ - INFO - [m.py:486] - Updated selected database for session d03fd32a-6f9a-4f99-8d4d-264701417904: chat_db
2025-06-29 02:22:36,252 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:36] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 02:22:36,304 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 02:22:36,306 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 02:22:36,306 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 02:22:36,310 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:22:36] "[35m[1mGET /api/tables?session_id=d03fd32a-6f9a-4f99-8d4d-264701417904&database=chat_db HTTP/1.1[0m" 500 -
2025-06-29 02:23:17,605 - __main__ - INFO - [m.py:486] - Updated selected database for session d03fd32a-6f9a-4f99-8d4d-264701417904: local
2025-06-29 02:23:17,607 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:23:17] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 02:23:17,722 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 02:23:17,724 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 02:23:17,725 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 02:23:17,726 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:23:17] "[35m[1mGET /api/tables?session_id=d03fd32a-6f9a-4f99-8d4d-264701417904&database=local HTTP/1.1[0m" 500 -
2025-06-29 02:23:25,322 - __main__ - INFO - [m.py:486] - Updated selected database for session d03fd32a-6f9a-4f99-8d4d-264701417904: chat_db
2025-06-29 02:23:25,325 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:23:25] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 02:23:25,429 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 02:23:25,430 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 02:23:25,430 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 02:23:25,432 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:23:25] "[35m[1mGET /api/tables?session_id=d03fd32a-6f9a-4f99-8d4d-264701417904&database=chat_db HTTP/1.1[0m" 500 -
2025-06-29 02:23:57,695 - __main__ - INFO - [m.py:909] - Attempting to connect to mongodb database for session 595b0096-8b1a-4ea3-8a68-82ab1a7460a6
2025-06-29 02:23:57,721 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session 595b0096-8b1a-4ea3-8a68-82ab1a7460a6
2025-06-29 02:23:57,725 - __main__ - INFO - [m.py:410] - Connection stored for session 595b0096-8b1a-4ea3-8a68-82ab1a7460a6: mongodb/
2025-06-29 02:23:57,738 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:23:57] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 02:23:57,917 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:23:57] "GET /api/databases?session_id=595b0096-8b1a-4ea3-8a68-82ab1a7460a6 HTTP/1.1" 200 -
2025-06-29 02:24:02,218 - __main__ - INFO - [m.py:486] - Updated selected database for session 595b0096-8b1a-4ea3-8a68-82ab1a7460a6: chat_app
2025-06-29 02:24:02,222 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:24:02] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 02:24:02,284 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 02:24:02,288 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 02:24:02,289 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 02:24:02,293 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 02:24:02] "[35m[1mGET /api/tables?session_id=595b0096-8b1a-4ea3-8a68-82ab1a7460a6&database=chat_app HTTP/1.1[0m" 500 -
2025-06-29 21:59:06,715 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-29 21:59:06,717 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-29 21:59:06,730 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 21:59:08,763 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-29 21:59:09,119 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 120-007-835
2025-06-29 21:59:39,621 - __main__ - INFO - [m.py:909] - Attempting to connect to mysql database for session e715f455-67fe-49e0-b8da-271f9b0e6035
2025-06-29 21:59:39,725 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-29 21:59:39,727 - mysql.connector - INFO - [__init__.py:152] - plugin_name: caching_sha2_password
2025-06-29 21:59:39,729 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin
2025-06-29 21:59:39,745 - mysql.connector - INFO - [__init__.py:151] - package: mysql.connector.plugins
2025-06-29 21:59:39,746 - mysql.connector - INFO - [__init__.py:152] - plugin_name: mysql_native_password
2025-06-29 21:59:39,749 - mysql.connector - INFO - [__init__.py:156] - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-06-29 21:59:39,796 - __main__ - INFO - [m.py:524] - Successfully connected to mysql database for session e715f455-67fe-49e0-b8da-271f9b0e6035
2025-06-29 21:59:39,797 - __main__ - INFO - [m.py:410] - Connection stored for session e715f455-67fe-49e0-b8da-271f9b0e6035: mysql/
2025-06-29 21:59:39,804 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 21:59:39] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 21:59:40,130 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 21:59:40] "GET /api/databases?session_id=e715f455-67fe-49e0-b8da-271f9b0e6035 HTTP/1.1" 200 -
2025-06-29 21:59:43,124 - __main__ - INFO - [m.py:486] - Updated selected database for session e715f455-67fe-49e0-b8da-271f9b0e6035: us_data
2025-06-29 21:59:43,127 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 21:59:43] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 21:59:43,256 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 21:59:43] "GET /api/tables?session_id=e715f455-67fe-49e0-b8da-271f9b0e6035&database=us_data HTTP/1.1" 200 -
2025-06-29 21:59:43,495 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 21:59:43] "GET /api/schema?session_id=e715f455-67fe-49e0-b8da-271f9b0e6035&database=us_data HTTP/1.1" 200 -
2025-06-29 22:00:16,093 - __main__ - INFO - [m.py:1056] - AI query request received: session_id=e715f455-67fe-49e0-b8da-271f9b0e6035, database=us_data, question='give top 3 name for us'
2025-06-29 22:00:16,210 - __main__ - INFO - [m.py:1130] - Retrieved schema for database us_data
2025-06-29 22:00:16,427 - __main__ - INFO - [m.py:1159] - Sending prompt to Groq API
2025-06-29 22:00:18,318 - httpx - INFO - [_client.py:1025] - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-29 22:00:18,346 - __main__ - INFO - [m.py:1171] - Received response from Groq API: <think>
Okay, so I need to generate a MySQL query ...
2025-06-29 22:00:18,350 - __main__ - INFO - [m.py:1192] - Executing generated query: 

SELECT DISTINCT first_name, last_name FROM us LI...
2025-06-29 22:00:18,407 - __main__ - INFO - [m.py:1203] - Query executed successfully, returned 3 results
2025-06-29 22:00:18,410 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 22:00:18] "POST /api/ai-query HTTP/1.1" 200 -
2025-06-29 22:06:46,503 - __main__ - INFO - [m.py:909] - Attempting to connect to mongodb database for session 242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb
2025-06-29 22:06:46,627 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session 242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb
2025-06-29 22:06:46,628 - __main__ - INFO - [m.py:410] - Connection stored for session 242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb: mongodb/
2025-06-29 22:06:46,639 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 22:06:46] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 22:06:46,927 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 22:06:46] "GET /api/databases?session_id=242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb HTTP/1.1" 200 -
2025-06-29 22:06:56,773 - __main__ - INFO - [m.py:486] - Updated selected database for session 242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb: chat_app
2025-06-29 22:06:56,775 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 22:06:56] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 22:06:56,896 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 22:06:56,901 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 22:06:56,903 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 22:06:56,907 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 22:06:56] "[35m[1mGET /api/tables?session_id=242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb&database=chat_app HTTP/1.1[0m" 500 -
2025-06-29 23:04:09,993 - __main__ - INFO - [m.py:439] - Cleaning up expired session: e715f455-67fe-49e0-b8da-271f9b0e6035
2025-06-29 23:04:10,020 - __main__ - INFO - [m.py:478] - Connection removed for session e715f455-67fe-49e0-b8da-271f9b0e6035
2025-06-29 23:09:10,125 - __main__ - INFO - [m.py:439] - Cleaning up expired session: 242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb
2025-06-29 23:09:10,138 - __main__ - INFO - [m.py:478] - Connection removed for session 242fddbb-6329-4fb3-9a4a-8bb0eda6a2fb
2025-06-29 23:31:50,821 - werkzeug - INFO - [_internal.py:97] -  * Detected change in '/home/<USER>/Desktop/OO/z.py', reloading
2025-06-29 23:31:52,973 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 23:31:55,235 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-29 23:31:55,249 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 120-007-835
2025-06-29 23:51:19,489 - werkzeug - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-06-29 23:51:19,490 - werkzeug - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-06-29 23:51:19,500 - werkzeug - INFO - [_internal.py:97] -  * Restarting with stat
2025-06-29 23:51:20,893 - werkzeug - WARNING - [_internal.py:97] -  * Debugger is active!
2025-06-29 23:51:20,896 - werkzeug - INFO - [_internal.py:97] -  * Debugger PIN: 120-007-835
2025-06-29 23:51:48,215 - __main__ - INFO - [m.py:909] - Attempting to connect to mongodb database for session 5eab90b6-70b0-4ed5-8bb6-d788500dfd6c
2025-06-29 23:51:48,335 - __main__ - INFO - [m.py:524] - Successfully connected to mongodb database for session 5eab90b6-70b0-4ed5-8bb6-d788500dfd6c
2025-06-29 23:51:48,335 - __main__ - INFO - [m.py:410] - Connection stored for session 5eab90b6-70b0-4ed5-8bb6-d788500dfd6c: mongodb/
2025-06-29 23:51:48,338 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 23:51:48] "POST /api/connect HTTP/1.1" 200 -
2025-06-29 23:51:48,741 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 23:51:48] "GET /api/databases?session_id=5eab90b6-70b0-4ed5-8bb6-d788500dfd6c HTTP/1.1" 200 -
2025-06-29 23:51:52,005 - __main__ - INFO - [m.py:486] - Updated selected database for session 5eab90b6-70b0-4ed5-8bb6-d788500dfd6c: chat_app
2025-06-29 23:51:52,006 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 23:51:52] "POST /api/select-database HTTP/1.1" 200 -
2025-06-29 23:51:52,133 - __main__ - ERROR - [m.py:285] - Connection error: Unsupported database type: mongodb
2025-06-29 23:51:52,135 - __main__ - ERROR - [m.py:617] - Failed to get tables: Unsupported database type: mongodb
2025-06-29 23:51:52,137 - __main__ - ERROR - [m.py:991] - Error fetching tables: Unsupported database type: mongodb
2025-06-29 23:51:52,139 - werkzeug - INFO - [_internal.py:97] - 127.0.0.1 - - [29/Jun/2025 23:51:52] "[35m[1mGET /api/tables?session_id=5eab90b6-70b0-4ed5-8bb6-d788500dfd6c&database=chat_app HTTP/1.1[0m" 500 -
